/**
 * @file line_following_test.c
 * @brief 循迹系统测试程序
 * <AUTHOR>
 * @date 2024
 */

#include "line_following.h"
#include "adc.h"
#include "uart.h"
#include <string.h>

/**
 * @brief 测试ADC传感器读取
 */
void test_sensor_reading(void)
{
    uart_print("=== Sensor Reading Test ===\r\n");
    
    uint16_t adc_values[8];
    
    for(int i = 0; i < 10; i++)
    {
        // 获取ADC值
        get_all_adc_values(adc_values);
        
        uart_print("ADC Values: ");
        for(uint8_t j = 0; j < 8; j++)
        {
            uart_print("%4d ", adc_values[j]);
        }
        uart_print("\r\n");
        
        HAL_Delay(500);
    }
    
    uart_print("Sensor reading test complete\r\n");
}

/**
 * @brief 测试传感器校准
 */
void test_sensor_calibration(void)
{
    uart_print("=== Sensor Calibration Test ===\r\n");
    
    // 初始化循迹系统
    line_following_init();
    
    // 执行校准
    uart_print("Starting calibration...\r\n");
    uart_print("Please move the robot over white and black areas\r\n");
    
    calibrate_line_sensors(3000); // 3秒校准
    
    uart_print("Calibration test complete\r\n");
}

/**
 * @brief 测试黑线位置计算
 */
void test_line_position_calculation(void)
{
    uart_print("=== Line Position Calculation Test ===\r\n");
    
    // 模拟不同的传感器状态
    struct {
        uint8_t digital_values[8];
        const char* description;
    } test_cases[] = {
        {{0,0,0,1,1,0,0,0}, "Center position"},
        {{1,1,0,0,0,0,0,0}, "Far left"},
        {{0,0,0,0,0,0,1,1}, "Far right"},
        {{0,1,1,1,0,0,0,0}, "Left side"},
        {{0,0,0,0,1,1,1,0}, "Right side"},
        {{1,1,1,1,1,1,1,1}, "All sensors (intersection)"},
        {{0,0,0,0,0,0,0,0}, "No line detected"}
    };
    
    int num_cases = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for(int i = 0; i < num_cases; i++)
    {
        uart_print("\nTest case %d: %s\r\n", i + 1, test_cases[i].description);
        
        // 设置传感器数据
        memcpy(line_sensor.digital_values, test_cases[i].digital_values, 8);
        
        // 模拟原始ADC值
        for(uint8_t j = 0; j < 8; j++)
        {
            line_sensor.raw_values[j] = test_cases[i].digital_values[j] ? 3000 : 1000;
        }
        
        // 计算黑线位置
        float position = calculate_line_position();
        
        uart_print("  Digital values: ");
        for(uint8_t j = 0; j < 8; j++)
        {
            uart_print("%d ", line_sensor.digital_values[j]);
        }
        uart_print("\r\n");
        uart_print("  Line position: %.2f\r\n", position);
        
        // 检测状态
        LineState_t state = detect_line_state();
        uart_print("  Detected state: %d\r\n", state);
    }
    
    uart_print("\nLine position calculation test complete\r\n");
}

/**
 * @brief 测试PID控制器
 */
void test_pid_controller(void)
{
    uart_print("=== PID Controller Test ===\r\n");
    
    // 初始化PID
    line_pid.kp = 2.0f;
    line_pid.ki = 0.1f;
    line_pid.kd = 0.5f;
    line_pid.max_output = 800;
    line_pid.error = 0;
    line_pid.last_error = 0;
    line_pid.integral = 0;
    
    // 测试不同的误差值
    float test_errors[] = {-2.0f, -1.0f, -0.5f, 0.0f, 0.5f, 1.0f, 2.0f};
    int num_errors = sizeof(test_errors) / sizeof(test_errors[0]);
    
    for(int i = 0; i < num_errors; i++)
    {
        float error = test_errors[i];
        float output = line_pid_calculate(0.0f, error);
        
        uart_print("Error: %6.2f, PID Output: %6.2f\r\n", error, output);
    }
    
    uart_print("PID controller test complete\r\n");
}

/**
 * @brief 测试电机控制
 */
void test_motor_control(void)
{
    uart_print("=== Motor Control Test ===\r\n");
    
    // 初始化电机参数
    motor_control.base_speed = 600;
    
    // 测试不同的PID输出
    float test_outputs[] = {-400, -200, -100, 0, 100, 200, 400};
    int num_outputs = sizeof(test_outputs) / sizeof(test_outputs[0]);
    
    for(int i = 0; i < num_outputs; i++)
    {
        float pid_output = test_outputs[i];
        
        // 计算电机速度
        int16_t left_speed = motor_control.base_speed - (int16_t)pid_output;
        int16_t right_speed = motor_control.base_speed + (int16_t)pid_output;
        
        uart_print("PID Output: %6.0f, Left: %4d, Right: %4d\r\n", 
                  pid_output, left_speed, right_speed);
    }
    
    uart_print("Motor control test complete\r\n");
}

/**
 * @brief 测试状态检测
 */
void test_state_detection(void)
{
    uart_print("=== State Detection Test ===\r\n");
    
    // 测试不同的传感器组合
    struct {
        uint8_t digital_values[8];
        float line_position;
        const char* expected_state;
    } test_cases[] = {
        {{0,0,1,1,1,0,0,0}, 0.0f, "NORMAL"},
        {{1,1,1,0,0,0,0,0}, -2.5f, "SHARP_LEFT"},
        {{0,0,0,0,0,1,1,1}, 2.5f, "SHARP_RIGHT"},
        {{0,1,1,1,0,0,0,0}, -1.2f, "LEFT_TURN"},
        {{0,0,0,1,1,1,0,0}, 1.2f, "RIGHT_TURN"},
        {{1,1,1,1,1,1,1,1}, 0.0f, "INTERSECTION"},
        {{0,0,0,0,0,0,0,0}, 0.0f, "LOST"}
    };
    
    int num_cases = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for(int i = 0; i < num_cases; i++)
    {
        uart_print("\nTest case %d: %s\r\n", i + 1, test_cases[i].expected_state);
        
        // 设置传感器数据
        memcpy(line_sensor.digital_values, test_cases[i].digital_values, 8);
        line_sensor.line_position = test_cases[i].line_position;
        
        // 计算检测到的传感器数量
        line_sensor.sensors_on_line = 0;
        for(uint8_t j = 0; j < 8; j++)
        {
            if(line_sensor.digital_values[j])
                line_sensor.sensors_on_line++;
        }
        
        // 检测状态
        LineState_t state = detect_line_state();
        
        uart_print("  Sensors: ");
        for(uint8_t j = 0; j < 8; j++)
        {
            uart_print("%d", line_sensor.digital_values[j]);
        }
        uart_print(", Position: %.2f, State: %d\r\n", 
                  line_sensor.line_position, state);
    }
    
    uart_print("State detection test complete\r\n");
}

/**
 * @brief 运行所有循迹系统测试
 */
void run_all_line_following_tests(void)
{
    uart_print("Starting line following system tests...\r\n\r\n");
    
    // 启动ADC转换
    start_adc_conversion();
    HAL_Delay(100);
    
    // 运行各项测试
    test_sensor_reading();
    HAL_Delay(1000);
    
    test_sensor_calibration();
    HAL_Delay(1000);
    
    test_line_position_calculation();
    HAL_Delay(1000);
    
    test_pid_controller();
    HAL_Delay(1000);
    
    test_motor_control();
    HAL_Delay(1000);
    
    test_state_detection();
    
    uart_print("\r\nAll line following tests completed!\r\n");
    uart_print("Please check the results above.\r\n");
    uart_print("You can now test the actual line following with commands:\r\n");
    uart_print("  $LC - Calibrate sensors\r\n");
    uart_print("  $L1 - Start line following\r\n");
    uart_print("  $LS - Show sensor status\r\n");
}

/**
 * @brief 实时传感器监控
 */
void monitor_sensors_realtime(void)
{
    uart_print("=== Real-time Sensor Monitor ===\r\n");
    uart_print("Press any key to stop monitoring...\r\n\r\n");
    
    while(1)
    {
        // 读取传感器数据
        read_line_sensors();
        
        // 计算黑线位置
        calculate_line_position();
        
        // 检测状态
        detect_line_state();
        
        // 显示信息
        uart_print("Raw: ");
        for(uint8_t i = 0; i < 8; i++)
        {
            uart_print("%4d ", line_sensor.raw_values[i]);
        }
        
        uart_print("| Digital: ");
        for(uint8_t i = 0; i < 8; i++)
        {
            uart_print("%d", line_sensor.digital_values[i]);
        }
        
        uart_print(" | Pos: %6.2f | State: %d | Sensors: %d\r\n",
                  line_sensor.line_position,
                  line_sensor.state,
                  line_sensor.sensors_on_line);
        
        HAL_Delay(200);
        
        // 检查是否有按键输入
        // 这里简化处理，实际应用中可以检查串口输入
        // if(uart_data_available()) break;
    }
    
    uart_print("Real-time monitoring stopped\r\n");
}
