/**
 * @file line_following.h
 * @brief MSPM0G3507八路灰度传感器循迹控制系统头文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef LINE_FOLLOWING_H
#define LINE_FOLLOWING_H

#include "ti_msp_dl_config.h"
#include "adc_config.h"
#include <stdint.h>
#include <stdbool.h>

/* 循迹系统参数定义 */
#define LINE_SENSOR_COUNT 8           // 传感器数量
#define LINE_THRESHOLD_DEFAULT 2000   // 默认黑线检测阈值 (0-4095)
#define LINE_WIDTH_CM 2               // 黑线宽度 (cm)
#define SENSOR_SPACING_MM 10          // 传感器间距 (mm)

/* 电机控制参数 */
#define MOTOR_BASE_SPEED 50           // 基础速度 (PWM占空比 0-100)
#define MOTOR_MAX_SPEED 80            // 最大速度
#define MOTOR_MIN_SPEED 20            // 最小速度
#define MOTOR_TURN_SPEED_RATIO 0.3f   // 转弯时内侧电机速度比例

/* PID控制参数 */
#define LINE_PID_KP 15.0f             // 比例系数
#define LINE_PID_KI 0.5f              // 积分系数
#define LINE_PID_KD 8.0f              // 微分系数
#define LINE_PID_MAX_OUTPUT 40        // PID最大输出

/* 循迹状态定义 */
typedef enum {
    LINE_STATE_NORMAL = 0,            // 正常循迹
    LINE_STATE_LOST,                  // 丢失黑线
    LINE_STATE_INTERSECTION,          // 十字路口
    LINE_STATE_LEFT_TURN,             // 左转弯
    LINE_STATE_RIGHT_TURN,            // 右转弯
    LINE_STATE_SHARP_LEFT,            // 急左转
    LINE_STATE_SHARP_RIGHT,           // 急右转
    LINE_STATE_END,                   // 终点
    LINE_STATE_STOP                   // 停止
} LineState_t;

/* 传感器数据结构 */
typedef struct {
    uint16_t raw_values[LINE_SENSOR_COUNT];     // 原始ADC值
    uint8_t digital_values[LINE_SENSOR_COUNT];  // 数字化值 (0/1)
    uint16_t threshold;                         // 检测阈值
    float line_position;                        // 黑线位置 (-3.5 到 +3.5)
    uint8_t sensors_on_line;                    // 检测到黑线的传感器数量
    LineState_t state;                          // 当前状态
    uint32_t last_seen_time;                    // 最后检测到黑线的时间
} LineSensor_t;

/* PID控制器结构 */
typedef struct {
    float kp, ki, kd;                 // PID参数
    float error;                      // 当前误差
    float last_error;                 // 上次误差
    float integral;                   // 积分值
    float derivative;                 // 微分值
    float output;                     // 输出值
    float max_output;                 // 最大输出限制
} LinePID_t;

/* 电机控制结构 */
typedef struct {
    int16_t left_speed;               // 左电机速度 (-100 到 +100)
    int16_t right_speed;              // 右电机速度 (-100 到 +100)
    uint16_t base_speed;              // 基础速度
    uint8_t direction;                // 运动方向 (0-前进, 1-后退)
} MotorControl_t;

/* 循迹系统配置结构 */
typedef struct {
    bool enabled;                     // 循迹使能
    bool debug_mode;                  // 调试模式
    uint16_t lost_timeout_ms;         // 丢线超时时间
    uint16_t turn_delay_ms;           // 转弯延时
    bool auto_calibration;            // 自动校准使能
    float speed_factor;               // 速度因子
} LineFollowConfig_t;

/* 外部变量声明 */
extern LineSensor_t line_sensor;
extern LinePID_t line_pid;
extern MotorControl_t motor_control;
extern LineFollowConfig_t line_config;

/* 函数声明 */

/**
 * @brief 初始化循迹系统
 */
void line_following_init(void);

/**
 * @brief 读取传感器数据
 */
void read_line_sensors(void);

/**
 * @brief 校准传感器
 * @param calibration_time_ms 校准时间(毫秒)
 */
void calibrate_line_sensors(uint16_t calibration_time_ms);

/**
 * @brief 计算黑线位置
 * @return 黑线位置 (-3.5 到 +3.5)
 */
float calculate_line_position(void);

/**
 * @brief 检测循迹状态
 * @return 当前状态
 */
LineState_t detect_line_state(void);

/**
 * @brief PID控制计算
 * @param setpoint 设定值 (通常为0)
 * @param measured_value 测量值 (黑线位置)
 * @return PID输出
 */
float line_pid_calculate(float setpoint, float measured_value);

/**
 * @brief 电机控制
 * @param pid_output PID输出值
 */
void control_motors(float pid_output);

/**
 * @brief 处理特殊状态 (转弯、路口等)
 */
void handle_special_states(void);

/**
 * @brief 循迹主控制函数
 */
void line_following_control(void);

/**
 * @brief 停止电机
 */
void stop_motors(void);

/**
 * @brief 设置电机速度
 * @param left_speed 左电机速度 (-100到+100)
 * @param right_speed 右电机速度 (-100到+100)
 */
void set_motor_speeds(int16_t left_speed, int16_t right_speed);

/**
 * @brief 获取传感器状态字符串
 * @param status_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_line_sensor_status(char *status_str, uint16_t max_len);

/**
 * @brief 设置循迹参数
 * @param kp PID比例系数
 * @param ki PID积分系数
 * @param kd PID微分系数
 * @param base_speed 基础速度
 */
void set_line_follow_params(float kp, float ki, float kd, uint16_t base_speed);

/**
 * @brief 循迹任务函数
 */
void line_following_task(void);

/**
 * @brief 获取系统时间(毫秒)
 */
uint32_t get_system_time_ms(void);

#endif /* LINE_FOLLOWING_H */
