# MSPM0G3507 SysConfig配置指南

## 概述

本文档说明如何在CCS的SysConfig工具中配置MSPM0G3507的外设，以支持八路灰度传感器循迹系统。

## 🔧 ADC配置

### 1. 添加ADC外设
1. 在SysConfig中点击左侧的 **ADC12**
2. 点击 **ADD** 添加ADC外设
3. 设置实例名称为 `ADC_VOLTAGE`

### 2. ADC基本配置
```
Clock Source: SYSOSC (32MHz)
Clock Divider: /1
Sample Rate: 4MHz
Conversion Mode: Single Conversion
Trigger Source: Software
Data Format: Binary, Right Aligned
Resolution: 12-bit
Reference Voltage: VDD (3.3V)
```

### 3. ADC通道配置
添加8个ADC通道，对应8路灰度传感器：

| 通道 | 引脚 | 内存结果 | 采样时间 |
|------|------|----------|----------|
| CH0 | PA27 | ADCMEM_ADC_CH0 | 8 cycles |
| CH1 | PA26 | ADCMEM_ADC_CH1 | 8 cycles |
| CH2 | PA25 | ADCMEM_ADC_CH2 | 8 cycles |
| CH3 | PA24 | ADCMEM_ADC_CH3 | 8 cycles |
| CH4 | PA17 | ADCMEM_ADC_CH4 | 8 cycles |
| CH5 | PA16 | ADCMEM_ADC_CH5 | 8 cycles |
| CH6 | PA15 | ADCMEM_ADC_CH6 | 8 cycles |
| CH7 | PA14 | ADCMEM_ADC_CH7 | 8 cycles |

## ⚡ PWM配置

### 1. 添加PWM外设
1. 在SysConfig中点击左侧的 **PWM**
2. 添加两个PWM实例：`PWM_0` (左电机) 和 `PWM_1` (右电机)

### 2. PWM基本配置
```
Timer Clock Source: BUSCLK (32MHz)
Timer Clock Prescaler: 32 (1MHz timer clock)
Timer Period: 1000 (1kHz PWM frequency)
PWM Mode: Edge Aligned
Initial Duty Cycle: 0%
```

### 3. PWM引脚配置

**PWM_0 (左电机):**
- CC0: PB20 (左电机PWM1)
- CC1: PB13 (左电机PWM2)

**PWM_1 (右电机):**
- CC0: PB4 (右电机PWM1)
- CC1: PB1 (右电机PWM2)

## 📡 UART配置

### 1. 添加UART外设
1. 在SysConfig中点击左侧的 **UART**
2. 添加UART实例：`UART_0`

### 2. UART基本配置
```
Baud Rate: 115200
Data Bits: 8
Parity: None
Stop Bits: 1
Flow Control: None
```

### 3. UART引脚配置
- TX: PA10
- RX: PA11

### 4. UART中断配置
- 启用RX中断
- 中断优先级: 1

## 🔌 GPIO配置

### 1. 添加GPIO外设
配置电机方向控制引脚：

| 功能 | 引脚 | 方向 | 初始状态 |
|------|------|------|----------|
| 左电机DIR1 | PB19 | Output | Low |
| 左电机DIR2 | PB12 | Output | Low |
| 右电机DIR1 | PB3 | Output | Low |
| 右电机DIR2 | PB0 | Output | Low |

## ⏰ Timer配置

### 1. 添加Timer外设
1. 在SysConfig中点击左侧的 **Timer**
2. 添加Timer实例：`TIMER_0`

### 2. Timer基本配置
```
Timer Mode: Periodic
Clock Source: BUSCLK (32MHz)
Clock Prescaler: 32 (1MHz timer clock)
Timer Period: 1000 (1ms period)
```

### 3. Timer中断配置
- 启用Timer中断
- 中断优先级: 2

## 🔧 系统配置

### 1. 时钟配置
```
SYSOSC: 32MHz (内部振荡器)
MCLK: 32MHz
BUSCLK: 32MHz
```

### 2. 电源配置
```
VDD: 3.3V
VREF: VDD
```

## 📝 配置步骤总结

1. **创建新工程**
   - 在CCS中创建空白工程
   - 选择MSPM0G3507器件

2. **打开SysConfig**
   - 双击 `empty.syscfg` 文件
   - 按照上述配置添加各个外设

3. **保存配置**
   - 使用 `Ctrl+S` 保存配置
   - SysConfig会自动生成 `ti_msp_dl_config.c` 和 `ti_msp_dl_config.h`

4. **添加源文件**
   - 将循迹系统的源文件添加到工程中
   - 配置包含路径

5. **编译下载**
   - 编译工程
   - 下载到开发板

## ⚠️ 注意事项

1. **引脚冲突检查**
   - 确保ADC引脚没有被其他外设占用
   - 检查PWM和GPIO引脚分配

2. **时钟配置**
   - 确保时钟频率设置正确
   - ADC和PWM的时钟源要匹配

3. **中断优先级**
   - UART中断优先级要高于Timer中断
   - 避免中断嵌套问题

4. **电源要求**
   - 确保传感器供电电压与MCU匹配
   - 检查电流消耗是否在范围内

## 🔍 调试建议

1. **使用串口调试**
   - 通过UART输出调试信息
   - 监控传感器数值和系统状态

2. **逐步测试**
   - 先测试ADC读取
   - 再测试PWM输出
   - 最后测试完整循迹功能

3. **参数调优**
   - 根据实际硬件调整PID参数
   - 调整检测阈值和速度参数

---
*配置完成后，系统即可支持八路灰度传感器的循迹功能*
