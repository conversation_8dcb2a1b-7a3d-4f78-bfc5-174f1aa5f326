/**
 * @file sensor_pin_test.c
 * @brief MSPM0G3507传感器引脚测试程序
 * <AUTHOR>
 * @date 2024
 */

#include "ti_msp_dl_config.h"
#include "digital_line_sensor.h"
#include <stdio.h>
#include <string.h>

/* 串口发送函数 */
void uart_send_string(char* str);
void test_sensor_pins(void);
void test_address_lines(void);
void test_sensor_reading(void);

/**
 * @brief 传感器引脚测试主函数
 */
void sensor_pin_test_main(void)
{
    char output_buffer[256];
    
    // 系统初始化
    SYSCFG_DL_init();
    
    uart_send_string("MSPM0G3507 Sensor Pin Test Program\r\n");
    uart_send_string("========================================\r\n");
    
    // 显示引脚分配
    uart_send_string("Pin Assignment:\r\n");
    uart_send_string("OUT: PA1  (A01)\r\n");
    uart_send_string("EN:  PA26\r\n");
    uart_send_string("AD2: PB21 (B21)\r\n");
    uart_send_string("AD1: PA9  (A09)\r\n");
    uart_send_string("AD0: PB6  (B06)\r\n");
    uart_send_string("ERR: PA16\r\n");
    uart_send_string("========================================\r\n\r\n");
    
    // 初始化传感器
    digital_line_sensor_init();
    uart_send_string("Sensor initialized\r\n\r\n");
    
    // 测试引脚功能
    test_sensor_pins();
    
    // 测试地址线
    test_address_lines();
    
    // 测试传感器读取
    test_sensor_reading();
    
    uart_send_string("All tests completed!\r\n");
}

/**
 * @brief 测试传感器引脚基本功能
 */
void test_sensor_pins(void)
{
    char output_buffer[128];
    
    uart_send_string("=== Pin Function Test ===\r\n");
    
    // 测试使能引脚
    uart_send_string("Testing EN pin (PA26)...\r\n");
    enable_sensor(false);
    delay_us(1000);
    uart_send_string("EN = Low\r\n");
    
    enable_sensor(true);
    delay_us(1000);
    uart_send_string("EN = High\r\n");
    
    // 测试错误检测引脚
    uart_send_string("Testing ERR pin (PA16)...\r\n");
    bool error_status = check_sensor_error();
    sprintf(output_buffer, "ERR pin status: %s\r\n", error_status ? "ERROR" : "OK");
    uart_send_string(output_buffer);
    
    // 测试输出引脚
    uart_send_string("Testing OUT pin (PA1)...\r\n");
    uint32_t out_state = DL_GPIO_readPins(SENSOR_OUT_PORT, SENSOR_OUT_PIN);
    sprintf(output_buffer, "OUT pin state: %s\r\n", out_state ? "HIGH" : "LOW");
    uart_send_string(output_buffer);
    
    uart_send_string("Pin function test completed\r\n\r\n");
}

/**
 * @brief 测试地址线功能
 */
void test_address_lines(void)
{
    char output_buffer[128];
    
    uart_send_string("=== Address Lines Test ===\r\n");
    
    for(uint8_t addr = 0; addr < 8; addr++)
    {
        uart_send_string("Setting address: ");
        sprintf(output_buffer, "%d (Binary: %d%d%d)\r\n", 
                addr,
                (addr & 0x04) ? 1 : 0,  // AD2
                (addr & 0x02) ? 1 : 0,  // AD1
                (addr & 0x01) ? 1 : 0); // AD0
        uart_send_string(output_buffer);
        
        // 设置地址
        set_sensor_address(addr);
        
        // 读取引脚状态验证
        uint32_t ad2_state = DL_GPIO_readPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
        uint32_t ad1_state = DL_GPIO_readPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
        uint32_t ad0_state = DL_GPIO_readPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
        
        sprintf(output_buffer, "Pin states - AD2(PB21): %d, AD1(PA9): %d, AD0(PB6): %d\r\n",
                ad2_state ? 1 : 0,
                ad1_state ? 1 : 0,
                ad0_state ? 1 : 0);
        uart_send_string(output_buffer);
        
        delay_us(10000); // 10ms延时
    }
    
    uart_send_string("Address lines test completed\r\n\r\n");
}

/**
 * @brief 测试传感器读取功能
 */
void test_sensor_reading(void)
{
    char output_buffer[256];
    
    uart_send_string("=== Sensor Reading Test ===\r\n");
    uart_send_string("Reading all 8 sensors...\r\n");
    
    for(int cycle = 0; cycle < 5; cycle++)
    {
        sprintf(output_buffer, "Cycle %d:\r\n", cycle + 1);
        uart_send_string(output_buffer);
        
        // 读取所有传感器
        read_all_sensors();
        
        // 显示结果
        uart_send_string("Sensor values: ");
        for(uint8_t i = 0; i < 8; i++)
        {
            sprintf(output_buffer, "%d", digital_line_sensor.digital_values[i]);
            uart_send_string(output_buffer);
        }
        uart_send_string("\r\n");
        
        // 显示黑线位置
        float position = calculate_digital_line_position();
        sprintf(output_buffer, "Line position: %.2f\r\n", position);
        uart_send_string(output_buffer);
        
        // 显示检测到的传感器数量
        sprintf(output_buffer, "Sensors on line: %d\r\n", digital_line_sensor.sensors_on_line);
        uart_send_string(output_buffer);
        
        // 显示错误状态
        sprintf(output_buffer, "Error flag: %s\r\n", digital_line_sensor.error_flag ? "ERROR" : "OK");
        uart_send_string(output_buffer);
        
        uart_send_string("---\r\n");
        
        // 延时1秒
        for(int i = 0; i < 1000; i++)
        {
            delay_us(1000);
        }
    }
    
    uart_send_string("Sensor reading test completed\r\n\r\n");
}

/**
 * @brief 单独测试每个传感器
 */
void test_individual_sensors(void)
{
    char output_buffer[128];
    
    uart_send_string("=== Individual Sensor Test ===\r\n");
    
    for(uint8_t sensor = 0; sensor < 8; sensor++)
    {
        sprintf(output_buffer, "Testing sensor %d:\r\n", sensor);
        uart_send_string(output_buffer);
        
        // 读取单个传感器
        uint8_t value = read_single_sensor(sensor);
        
        sprintf(output_buffer, "  Value: %d (%s)\r\n", 
                value, value ? "Black Line" : "White");
        uart_send_string(output_buffer);
        
        delay_us(50000); // 50ms延时
    }
    
    uart_send_string("Individual sensor test completed\r\n\r\n");
}

/**
 * @brief 连续监控模式
 */
void continuous_monitor_mode(void)
{
    char output_buffer[128];
    uint32_t count = 0;
    
    uart_send_string("=== Continuous Monitor Mode ===\r\n");
    uart_send_string("Monitoring sensors... (will run 50 cycles)\r\n");
    
    while(count < 50)
    {
        // 读取所有传感器
        read_all_sensors();
        
        // 每10次显示一次结果
        if(count % 10 == 0)
        {
            sprintf(output_buffer, "Count %lu: ", count);
            uart_send_string(output_buffer);
            
            for(uint8_t i = 0; i < 8; i++)
            {
                sprintf(output_buffer, "%d", digital_line_sensor.digital_values[i]);
                uart_send_string(output_buffer);
            }
            
            sprintf(output_buffer, " | Pos: %.2f | Sensors: %d\r\n",
                    digital_line_sensor.line_position,
                    digital_line_sensor.sensors_on_line);
            uart_send_string(output_buffer);
        }
        
        count++;
        
        // 延时100ms
        for(int i = 0; i < 100; i++)
        {
            delay_us(1000);
        }
    }
    
    uart_send_string("Continuous monitor completed\r\n\r\n");
}

/**
 * @brief 串口发送字符串
 */
void uart_send_string(char* str)
{
    while(*str != 0 && str != 0)
    {
        while(DL_UART_isBusy(UART_0_INST) == true);
        DL_UART_Main_transmitData(UART_0_INST, *str++);
    }
}
