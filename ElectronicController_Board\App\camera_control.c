/**
 * @file camera_control.c
 * @brief 摄像头控制和数据处理实现文件
 * <AUTHOR>
 * @date 2024
 */

#include "camera_control.h"
#include "usart.h"
#include "uart.h"
#include <string.h>
#include <stdio.h>

/* 全局变量定义 */
GimbalControl_t gimbal_control;

/**
 * @brief 初始化云台控制参数
 */
void init_gimbal_control(void)
{
    gimbal_control.pixel_to_step_x = GIMBAL_PIXEL_TO_STEP_X;
    gimbal_control.pixel_to_step_y = GIMBAL_PIXEL_TO_STEP_Y;
    gimbal_control.dead_zone_x = GIMBAL_DEAD_ZONE_X;
    gimbal_control.dead_zone_y = GIMBAL_DEAD_ZONE_Y;
    gimbal_control.max_step_per_cycle = GIMBAL_MAX_STEP_PER_CYCLE;
    gimbal_control.confidence_threshold = 30; // 默认置信度阈值30%
}

/**
 * @brief 发送命令到摄像头
 * @param cmd 命令类型
 * @param data 数据指针
 * @param len 数据长度
 * @return 发送结果 0-成功 1-失败
 */
uint8_t send_camera_command(uint8_t cmd, uint8_t *data, uint8_t len)
{
    uint8_t frame_buffer[64];
    uint16_t frame_len = 0;
    
    // 检查数据长度
    if (len > 56) // 64 - 4(header) - 4(checksum) = 56
    {
        return 1;
    }
    
    // 构建帧头
    frame_buffer[frame_len++] = CAM_FRAME_HEADER1;
    frame_buffer[frame_len++] = CAM_FRAME_HEADER2;
    frame_buffer[frame_len++] = cmd;
    frame_buffer[frame_len++] = len;
    
    // 复制数据
    if (data != NULL && len > 0)
    {
        memcpy(&frame_buffer[frame_len], data, len);
        frame_len += len;
    }
    
    // 计算校验和
    uint8_t checksum = calculate_frame_checksum(&frame_buffer[2], frame_len - 2);
    frame_buffer[frame_len++] = checksum;
    
    // 发送数据
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart3, frame_buffer, frame_len, 1000);
    
    return (status == HAL_OK) ? 0 : 1;
}

/**
 * @brief 设置摄像头阈值
 * @param threshold 阈值(0-255)
 * @return 设置结果 0-成功 1-失败
 */
uint8_t set_camera_threshold(uint8_t threshold)
{
    uint8_t data[1];
    data[0] = threshold;
    
    return send_camera_command(CAM_CMD_SET_THRESHOLD, data, 1);
}

/**
 * @brief 设置摄像头感兴趣区域
 * @param x 左上角X坐标
 * @param y 左上角Y坐标
 * @param width 宽度
 * @param height 高度
 * @return 设置结果 0-成功 1-失败
 */
uint8_t set_camera_roi(int16_t x, int16_t y, int16_t width, int16_t height)
{
    uint8_t data[8];
    
    // 打包ROI数据（小端字节序）
    data[0] = x & 0xFF;
    data[1] = (x >> 8) & 0xFF;
    data[2] = y & 0xFF;
    data[3] = (y >> 8) & 0xFF;
    data[4] = width & 0xFF;
    data[5] = (width >> 8) & 0xFF;
    data[6] = height & 0xFF;
    data[7] = (height >> 8) & 0xFF;
    
    return send_camera_command(CAM_CMD_SET_ROI, data, 8);
}

/**
 * @brief 获取摄像头状态信息
 * @param status_str 状态字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_camera_status(char *status_str, uint16_t max_len)
{
    if (status_str == NULL || max_len == 0)
    {
        return;
    }
    
    snprintf(status_str, max_len,
             "Camera Status:\r\n"
             "Target Found: %s\r\n"
             "Center: (%d, %d)\r\n"
             "Rectangle: (%d, %d, %d, %d)\r\n"
             "Confidence: %d%%\r\n"
             "Frame ID: %lu\r\n"
             "Gimbal Control:\r\n"
             "Dead Zone: (%d, %d)\r\n"
             "Pixel to Step: (%.1f, %.1f)\r\n"
             "Max Step: %ld\r\n",
             camera_data.target_found ? "YES" : "NO",
             camera_data.center_x, camera_data.center_y,
             camera_data.rect_x, camera_data.rect_y,
             camera_data.rect_width, camera_data.rect_height,
             camera_data.confidence,
             camera_data.frame_id,
             gimbal_control.dead_zone_x, gimbal_control.dead_zone_y,
             gimbal_control.pixel_to_step_x, gimbal_control.pixel_to_step_y,
             gimbal_control.max_step_per_cycle);
}

/**
 * @brief 验证矩形数据的合理性
 * @param rect_data 矩形数据指针
 * @return 验证结果 0-合理 1-不合理
 */
uint8_t validate_rect_data(RectData_t *rect_data)
{
    // 检查坐标范围
    if (rect_data->center_x < 0 || rect_data->center_x >= CAM_WIDTH ||
        rect_data->center_y < 0 || rect_data->center_y >= CAM_HEIGHT)
    {
        return 1;
    }
    
    // 检查矩形范围
    if (rect_data->rect_x < 0 || rect_data->rect_y < 0 ||
        rect_data->width <= 0 || rect_data->height <= 0 ||
        (rect_data->rect_x + rect_data->width) > CAM_WIDTH ||
        (rect_data->rect_y + rect_data->height) > CAM_HEIGHT)
    {
        return 1;
    }
    
    // 检查置信度
    if (rect_data->confidence > 100)
    {
        return 1;
    }
    
    // 检查中心点是否在矩形内
    if (rect_data->center_x < rect_data->rect_x ||
        rect_data->center_x > (rect_data->rect_x + rect_data->width) ||
        rect_data->center_y < rect_data->rect_y ||
        rect_data->center_y > (rect_data->rect_y + rect_data->height))
    {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 计算目标跟踪的优先级
 * @param rect_data 矩形数据指针
 * @return 优先级分数 (0-100)
 */
uint8_t calculate_target_priority(RectData_t *rect_data)
{
    uint8_t priority = 0;
    
    // 基础置信度权重 (40%)
    priority += (rect_data->confidence * 40) / 100;
    
    // 目标大小权重 (30%) - 适中大小的目标优先级更高
    uint32_t area = rect_data->width * rect_data->height;
    uint32_t ideal_area = (CAM_WIDTH * CAM_HEIGHT) / 16; // 理想面积为1/16屏幕
    uint32_t area_diff = (area > ideal_area) ? (area - ideal_area) : (ideal_area - area);
    uint8_t size_score = 30 - (area_diff * 30) / ideal_area;
    if (size_score > 30) size_score = 30;
    priority += size_score;
    
    // 位置权重 (30%) - 靠近屏幕中心的目标优先级更高
    int16_t center_x_diff = abs(rect_data->center_x - CAM_WIDTH / 2);
    int16_t center_y_diff = abs(rect_data->center_y - CAM_HEIGHT / 2);
    uint16_t distance_from_center = center_x_diff + center_y_diff;
    uint16_t max_distance = CAM_WIDTH / 2 + CAM_HEIGHT / 2;
    uint8_t position_score = 30 - (distance_from_center * 30) / max_distance;
    priority += position_score;
    
    return priority;
}

/**
 * @brief 摄像头数据统计信息
 */
typedef struct
{
    uint32_t total_frames;      // 总帧数
    uint32_t valid_frames;      // 有效帧数
    uint32_t error_frames;      // 错误帧数
    uint32_t target_frames;     // 检测到目标的帧数
    uint32_t last_frame_id;     // 最后一帧ID
    uint8_t avg_confidence;     // 平均置信度
} CameraStats_t;

static CameraStats_t camera_stats = {0};

/**
 * @brief 更新摄像头统计信息
 * @param rect_data 矩形数据指针
 * @param is_valid 数据是否有效
 */
void update_camera_stats(RectData_t *rect_data, uint8_t is_valid)
{
    camera_stats.total_frames++;
    
    if (is_valid)
    {
        camera_stats.valid_frames++;
        if (rect_data->confidence >= gimbal_control.confidence_threshold)
        {
            camera_stats.target_frames++;
        }
        
        // 更新平均置信度（简单移动平均）
        camera_stats.avg_confidence = 
            (camera_stats.avg_confidence * 9 + rect_data->confidence) / 10;
        
        camera_stats.last_frame_id = rect_data->frame_id;
    }
    else
    {
        camera_stats.error_frames++;
    }
}

/**
 * @brief 获取摄像头统计信息
 * @param stats_str 统计信息字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_camera_stats(char *stats_str, uint16_t max_len)
{
    if (stats_str == NULL || max_len == 0)
    {
        return;
    }
    
    uint8_t success_rate = (camera_stats.total_frames > 0) ? 
        (camera_stats.valid_frames * 100) / camera_stats.total_frames : 0;
    
    uint8_t target_rate = (camera_stats.valid_frames > 0) ? 
        (camera_stats.target_frames * 100) / camera_stats.valid_frames : 0;
    
    snprintf(stats_str, max_len,
             "Camera Statistics:\r\n"
             "Total Frames: %lu\r\n"
             "Valid Frames: %lu (%d%%)\r\n"
             "Error Frames: %lu\r\n"
             "Target Frames: %lu (%d%%)\r\n"
             "Last Frame ID: %lu\r\n"
             "Avg Confidence: %d%%\r\n",
             camera_stats.total_frames,
             camera_stats.valid_frames, success_rate,
             camera_stats.error_frames,
             camera_stats.target_frames, target_rate,
             camera_stats.last_frame_id,
             camera_stats.avg_confidence);
}
