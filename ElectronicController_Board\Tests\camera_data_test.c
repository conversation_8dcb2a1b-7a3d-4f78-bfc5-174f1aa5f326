/**
 * @file camera_data_test.c
 * @brief 摄像头数据解析测试程序
 * <AUTHOR>
 * @date 2024
 */

#include "camera_control.h"
#include "uart.h"
#include <string.h>

/**
 * @brief 创建测试用的矩形数据包
 * @param buffer 输出缓冲区
 * @param center_x 矩形中心X坐标
 * @param center_y 矩形中心Y坐标
 * @param width 矩形宽度
 * @param height 矩形高度
 * @param confidence 置信度
 * @param frame_id 帧ID
 * @return 数据包长度
 */
uint16_t create_test_rect_packet(uint8_t *buffer, 
                                int16_t center_x, int16_t center_y,
                                int16_t width, int16_t height,
                                uint8_t confidence, uint32_t frame_id)
{
    uint16_t len = 0;
    
    // 帧头
    buffer[len++] = CAM_FRAME_HEADER1;  // 0xFF
    buffer[len++] = CAM_FRAME_HEADER2;  // 0xA5
    buffer[len++] = CAM_CMD_RECT_DATA;  // 0x01
    buffer[len++] = sizeof(RectData_t); // 数据长度
    
    // 矩形数据
    RectData_t *rect_data = (RectData_t*)&buffer[len];
    
    rect_data->center_x = center_x;
    rect_data->center_y = center_y;
    rect_data->rect_x = center_x - width / 2;
    rect_data->rect_y = center_y - height / 2;
    rect_data->width = width;
    rect_data->height = height;
    rect_data->confidence = confidence;
    rect_data->frame_id = frame_id;
    
    // 计算校验和（不包括校验和字段本身）
    rect_data->checksum = calculate_frame_checksum((uint8_t*)rect_data, sizeof(RectData_t) - 1);
    
    len += sizeof(RectData_t);
    
    return len;
}

/**
 * @brief 测试摄像头数据解析功能
 */
void test_camera_data_parsing(void)
{
    uint8_t test_buffer[64];
    uint16_t packet_len;
    
    uart_print("=== Camera Data Parsing Test ===\r\n");
    
    // 测试用例1: 正常的矩形数据
    uart_print("\nTest 1: Normal rectangle data\r\n");
    packet_len = create_test_rect_packet(test_buffer, 160, 120, 50, 40, 85, 1001);
    
    // 清空摄像头数据
    memset(&camera_data, 0, sizeof(Camera_t));
    
    // 处理数据
    process_camera_data(test_buffer, packet_len);
    
    // 验证结果
    if (camera_data.target_found)
    {
        uart_print("✓ Target detected successfully\r\n");
        uart_print("  Center: (%d, %d)\r\n", camera_data.center_x, camera_data.center_y);
        uart_print("  Rectangle: (%d, %d, %d, %d)\r\n", 
                  camera_data.rect_x, camera_data.rect_y,
                  camera_data.rect_width, camera_data.rect_height);
        uart_print("  Confidence: %d%%\r\n", camera_data.confidence);
        uart_print("  Frame ID: %lu\r\n", camera_data.frame_id);
    }
    else
    {
        uart_print("✗ Target detection failed\r\n");
    }
    
    // 测试用例2: 低置信度数据（应该被拒绝）
    uart_print("\nTest 2: Low confidence data (should be rejected)\r\n");
    packet_len = create_test_rect_packet(test_buffer, 160, 120, 50, 40, 20, 1002);
    
    // 清空摄像头数据
    memset(&camera_data, 0, sizeof(Camera_t));
    
    // 处理数据
    process_camera_data(test_buffer, packet_len);
    
    if (!camera_data.target_found)
    {
        uart_print("✓ Low confidence data correctly rejected\r\n");
    }
    else
    {
        uart_print("✗ Low confidence data should be rejected\r\n");
    }
    
    // 测试用例3: 超出边界的数据（应该被拒绝）
    uart_print("\nTest 3: Out of bounds data (should be rejected)\r\n");
    packet_len = create_test_rect_packet(test_buffer, 400, 300, 50, 40, 80, 1003);
    
    // 清空摄像头数据
    memset(&camera_data, 0, sizeof(Camera_t));
    
    // 处理数据
    process_camera_data(test_buffer, packet_len);
    
    if (!camera_data.target_found)
    {
        uart_print("✓ Out of bounds data correctly rejected\r\n");
    }
    else
    {
        uart_print("✗ Out of bounds data should be rejected\r\n");
    }
    
    // 测试用例4: 错误的校验和（应该被拒绝）
    uart_print("\nTest 4: Invalid checksum (should be rejected)\r\n");
    packet_len = create_test_rect_packet(test_buffer, 160, 120, 50, 40, 80, 1004);
    
    // 故意破坏校验和
    test_buffer[packet_len - 1] = 0x00;
    
    // 清空摄像头数据
    memset(&camera_data, 0, sizeof(Camera_t));
    
    // 处理数据
    process_camera_data(test_buffer, packet_len);
    
    if (!camera_data.target_found)
    {
        uart_print("✓ Invalid checksum correctly rejected\r\n");
    }
    else
    {
        uart_print("✗ Invalid checksum should be rejected\r\n");
    }
    
    uart_print("\n=== Test Complete ===\r\n");
}

/**
 * @brief 测试云台控制算法
 */
void test_gimbal_control(void)
{
    uart_print("\n=== Gimbal Control Test ===\r\n");
    
    // 初始化云台控制参数
    init_gimbal_control();
    
    // 模拟不同的目标位置
    struct {
        int16_t center_x;
        int16_t center_y;
        const char* description;
    } test_cases[] = {
        {160, 120, "Center position (no adjustment needed)"},
        {200, 120, "Right of center"},
        {120, 120, "Left of center"},
        {160, 160, "Below center"},
        {160, 80,  "Above center"},
        {200, 160, "Bottom-right"},
        {120, 80,  "Top-left"}
    };
    
    int num_cases = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for (int i = 0; i < num_cases; i++)
    {
        uart_print("\nTest case %d: %s\r\n", i + 1, test_cases[i].description);
        
        // 设置摄像头数据
        camera_data.center_x = test_cases[i].center_x;
        camera_data.center_y = test_cases[i].center_y;
        camera_data.target_found = 1;
        camera_data.confidence = 80;
        
        // 计算误差
        int16_t error_x = camera_data.center_x - CAM_WIDTH / 2;
        int16_t error_y = camera_data.center_y - CAM_HEIGHT / 2;
        
        uart_print("  Target: (%d, %d)\r\n", camera_data.center_x, camera_data.center_y);
        uart_print("  Error: (%d, %d) pixels\r\n", error_x, error_y);
        
        // 计算需要的调整步数
        int32_t step_x = (int32_t)(error_x * gimbal_control.pixel_to_step_x);
        int32_t step_y = (int32_t)(error_y * gimbal_control.pixel_to_step_y);
        
        uart_print("  Required steps: (%ld, %ld)\r\n", -step_x, -step_y);
        
        // 检查是否在死区内
        if (abs(error_x) < gimbal_control.dead_zone_x && 
            abs(error_y) < gimbal_control.dead_zone_y)
        {
            uart_print("  Status: Within dead zone, no adjustment needed\r\n");
        }
        else
        {
            uart_print("  Status: Adjustment required\r\n");
        }
    }
    
    uart_print("\n=== Gimbal Control Test Complete ===\r\n");
}

/**
 * @brief 测试摄像头命令发送
 */
void test_camera_commands(void)
{
    uart_print("\n=== Camera Command Test ===\r\n");
    
    // 测试设置阈值命令
    uart_print("Testing threshold setting...\r\n");
    if (set_camera_threshold(128) == 0)
    {
        uart_print("✓ Threshold command sent successfully\r\n");
    }
    else
    {
        uart_print("✗ Failed to send threshold command\r\n");
    }
    
    // 测试设置ROI命令
    uart_print("Testing ROI setting...\r\n");
    if (set_camera_roi(80, 60, 160, 120) == 0)
    {
        uart_print("✓ ROI command sent successfully\r\n");
    }
    else
    {
        uart_print("✗ Failed to send ROI command\r\n");
    }
    
    uart_print("=== Camera Command Test Complete ===\r\n");
}

/**
 * @brief 运行所有测试
 */
void run_all_camera_tests(void)
{
    uart_print("Starting camera gimbal system tests...\r\n");
    
    // 运行各项测试
    test_camera_data_parsing();
    test_gimbal_control();
    test_camera_commands();
    
    uart_print("\nAll tests completed!\r\n");
    uart_print("Please check the results above.\r\n");
}

/**
 * @brief 生成示例数据包用于测试
 */
void generate_sample_data_packets(void)
{
    uart_print("\n=== Sample Data Packets ===\r\n");
    
    uint8_t buffer[64];
    uint16_t len;
    
    // 生成几个示例数据包
    struct {
        int16_t center_x, center_y, width, height;
        uint8_t confidence;
        const char* description;
    } samples[] = {
        {160, 120, 50, 40, 85, "Center target"},
        {200, 100, 30, 30, 70, "Small target right-top"},
        {120, 140, 80, 60, 90, "Large target left-bottom"}
    };
    
    for (int i = 0; i < 3; i++)
    {
        len = create_test_rect_packet(buffer, 
                                     samples[i].center_x, samples[i].center_y,
                                     samples[i].width, samples[i].height,
                                     samples[i].confidence, 2000 + i);
        
        uart_print("\n%s:\r\n", samples[i].description);
        uart_print("Hex data: ");
        for (uint16_t j = 0; j < len; j++)
        {
            uart_print("%02X ", buffer[j]);
        }
        uart_print("\r\n");
    }
    
    uart_print("\n=== Sample Data Generation Complete ===\r\n");
}
