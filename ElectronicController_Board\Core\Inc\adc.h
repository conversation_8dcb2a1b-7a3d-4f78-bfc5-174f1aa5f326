/**
 * @file adc.h
 * @brief ADC配置和控制头文件 - 用于八路灰度传感器
 * <AUTHOR>
 * @date 2024
 */

#ifndef __ADC_H__
#define __ADC_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"

/* 灰度传感器数量定义 */
#define GRAY_SENSOR_COUNT 8

/* 外部变量声明 */
extern ADC_HandleTypeDef hadc1;
extern DMA_HandleTypeDef hdma_adc1;
extern uint16_t adc_buffer[GRAY_SENSOR_COUNT];

/* 函数声明 */

/**
 * @brief ADC1初始化函数
 */
void MX_ADC1_Init(void);

/**
 * @brief 启动ADC DMA转换
 */
void start_adc_conversion(void);

/**
 * @brief 停止ADC DMA转换
 */
void stop_adc_conversion(void);

/**
 * @brief 获取ADC转换结果
 * @param channel 通道号 (0-7)
 * @return ADC值 (0-4095)
 */
uint16_t get_adc_value(uint8_t channel);

/**
 * @brief 获取所有ADC转换结果
 * @param values 输出数组指针
 */
void get_all_adc_values(uint16_t *values);

/**
 * @brief 检查ADC转换是否完成
 * @return 1-完成 0-未完成
 */
uint8_t is_adc_conversion_complete(void);

#ifdef __cplusplus
}
#endif

#endif /* __ADC_H__ */
