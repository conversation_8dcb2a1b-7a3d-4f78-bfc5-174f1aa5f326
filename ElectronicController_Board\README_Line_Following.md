# 八路灰度传感器循迹控制系统

## 概述

本系统基于STM32F4实现了一个智能循迹小车控制系统，使用八路灰度传感器检测2cm宽的黑线，支持1米正方形轨道的循迹和直角转弯功能。

## 主要功能

### 1. 八路灰度传感器检测
- 支持8路模拟灰度传感器，通过ADC采集
- 自动校准功能，适应不同环境光线
- 实时数字化处理，准确检测黑线位置

### 2. 智能循迹算法
- 基于加权平均算法计算黑线位置
- PID控制算法实现精确跟线
- 支持多种状态检测：直线、转弯、十字路口、丢线等

### 3. 电机控制系统
- 双电机差速控制
- 支持前进、后退、原地转向
- 速度自适应调整

### 4. 直角转弯处理
- 自动检测直角转弯
- 原地转向算法
- 转弯完成后自动恢复循迹

## 硬件连接

### 八路灰度传感器连接
```
传感器编号  STM32引脚   ADC通道    说明
传感器0     PA0        ADC1_IN0   最左侧传感器
传感器1     PA1        ADC1_IN1   
传感器2     PC0        ADC1_IN10  避免与UART2冲突
传感器3     PC1        ADC1_IN11  
传感器4     PC2        ADC1_IN12  中心区域
传感器5     PC3        ADC1_IN13  
传感器6     PB0        ADC1_IN8   
传感器7     PB1        ADC1_IN9   最右侧传感器
```

### 电机连接
- **左电机**: UART2 (PA2/PA3) - 地址0x01
- **右电机**: UART5 - 地址0x01

### 传感器布局
```
    2cm黑线
[0][1][2][3][4][5][6][7]
←─────── 70mm ────────→
```

## 软件架构

### 核心文件
- `adc.c/h`: ADC配置和传感器数据采集
- `line_following.c/h`: 循迹控制算法实现
- `project_core.c`: 主程序集成
- `Examples/line_following_example.c`: 使用示例

### 任务调度
- **line_follow_task (20ms)**: 循迹控制主任务
- **ADC DMA**: 连续采集传感器数据
- **PID控制**: 实时计算电机控制输出

## 控制算法

### 1. 传感器数据处理
```c
// 数字化处理
for(uint8_t i = 0; i < 8; i++) {
    if(adc_value[i] > threshold) {
        digital_value[i] = 1;  // 检测到黑线
    } else {
        digital_value[i] = 0;  // 白色区域
    }
}
```

### 2. 黑线位置计算
```c
// 加权平均算法
float position = weighted_sum / total_weight;
// 位置范围: -3.5 到 +3.5
```

### 3. PID控制
```c
error = 0 - line_position;  // 设定值为0(中心)
output = Kp*error + Ki*integral + Kd*derivative;
left_speed = base_speed - output;
right_speed = base_speed + output;
```

### 4. 状态检测
- **NORMAL**: 1-3个传感器检测到黑线，正常循迹
- **LEFT_TURN/RIGHT_TURN**: 黑线偏向一侧，需要转弯
- **SHARP_LEFT/SHARP_RIGHT**: 急转弯，执行原地转向
- **INTERSECTION**: 多个传感器检测到黑线，十字路口
- **LOST**: 没有传感器检测到黑线，丢线状态

## 串口控制命令

### 基本控制
- `$L1`: 启动循迹
- `$L0`: 停止循迹
- `$LS`: 显示传感器状态
- `$LT`: 显示统计信息
- `$LC`: 重新校准传感器

### 调试命令
- `$D1`: 显示摄像头状态
- `$D2`: 显示电机状态

## 参数配置

### PID参数
```c
#define LINE_PID_KP 2.0f      // 比例系数
#define LINE_PID_KI 0.1f      // 积分系数  
#define LINE_PID_KD 0.5f      // 微分系数
```

### 电机参数
```c
#define MOTOR_BASE_SPEED 800          // 基础速度
#define MOTOR_MAX_SPEED 1500          // 最大速度
#define MOTOR_TURN_SPEED_RATIO 0.3f   // 转弯速度比例
```

### 检测参数
```c
#define LINE_THRESHOLD_DEFAULT 2000   // 黑线检测阈值
#define SENSOR_SPACING_MM 10          // 传感器间距
```

## 使用步骤

### 1. 硬件准备
1. 按照连接图连接八路灰度传感器
2. 连接左右电机到对应串口
3. 确保传感器供电正常(3.3V或5V)

### 2. 软件配置
1. 编译并下载程序到STM32
2. 通过串口调试工具连接USART1
3. 发送`$LC`进行传感器校准

### 3. 校准过程
1. 将小车放在轨道上
2. 发送校准命令后，手动移动小车
3. 确保传感器经过白色和黑色区域
4. 校准完成后系统自动计算阈值

### 4. 开始循迹
1. 将小车放在黑线上
2. 发送`$L1`启动循迹
3. 小车开始自动循迹

## 轨道要求

### 黑线规格
- **宽度**: 2cm
- **颜色**: 黑色(反射率低)
- **背景**: 白色或浅色(反射率高)

### 轨道设计
- **形状**: 1米×1米正方形
- **转弯**: 90度直角转弯
- **线条**: 连续无断点

### 安装要求
- 传感器距离地面: 2-5mm
- 传感器阵列中心对准黑线
- 确保传感器水平安装

## 参数调优指南

### 1. PID参数调整
```
KP过大: 系统振荡，左右摆动
KP过小: 响应慢，转弯不及时
KI过大: 系统不稳定
KD过大: 对噪声敏感
KD过小: 超调严重
```

### 2. 速度参数调整
```
基础速度过快: 转弯不及时，冲出轨道
基础速度过慢: 循迹效率低
转弯速度比例: 影响转弯灵活性
```

### 3. 检测阈值调整
```
阈值过高: 检测不到黑线
阈值过低: 误检测白色区域
建议使用自动校准功能
```

## 故障排除

### 常见问题

1. **传感器读数异常**
   - 检查ADC配置和引脚连接
   - 确认传感器供电电压
   - 重新校准传感器

2. **循迹不稳定**
   - 调整PID参数，特别是KP和KD
   - 检查传感器安装高度
   - 降低基础速度

3. **转弯不准确**
   - 调整转弯延时参数
   - 检查左右电机速度匹配
   - 调整急转弯检测阈值

4. **频繁丢线**
   - 检查黑线对比度
   - 调整检测阈值
   - 增加丢线超时时间

5. **电机不响应**
   - 检查串口连接和波特率
   - 确认电机地址设置
   - 检查电机驱动器状态

### 调试方法
1. 使用`$LS`查看传感器实时状态
2. 使用`$LT`查看循迹统计信息
3. 启用调试模式观察控制过程
4. 逐步调整参数并测试效果

## 性能优化

### 1. 算法优化
- 使用卡尔曼滤波减少传感器噪声
- 实现预测控制算法
- 添加自适应参数调整

### 2. 硬件优化
- 使用高精度ADC
- 添加传感器滤波电路
- 优化传感器布局

### 3. 系统优化
- 合理分配任务优先级
- 优化内存使用
- 实现看门狗保护

## 扩展功能

### 可添加的功能
1. 多种轨道模式支持
2. 速度自适应控制
3. 远程监控界面
4. 数据记录和分析
5. 多车协同控制

---
*本文档版本: v1.0*  
*最后更新: 2024年*
