/**
 * @file adc.c
 * @brief ADC配置和控制文件 - 用于八路灰度传感器
 * <AUTHOR>
 * @date 2024
 */

#include "adc.h"

ADC_HandleTypeDef hadc1;
DMA_HandleTypeDef hdma_adc1;

/* ADC采集缓冲区 - 8路灰度传感器 */
uint16_t adc_buffer[GRAY_SENSOR_COUNT];
uint8_t adc_conversion_complete = 0;

/**
 * @brief ADC1初始化函数
 */
void MX_ADC1_Init(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};

    /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
    */
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1.Init.ScanConvMode = ENABLE;
    hadc1.Init.ContinuousConvMode = ENABLE;
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1.Init.NbrOfConversion = GRAY_SENSOR_COUNT;
    hadc1.Init.DMAContinuousRequests = ENABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SEQ_CONV;
    
    if (HAL_ADC_Init(&hadc1) != HAL_OK)
    {
        Error_Handler();
    }

    /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
    */
    // 通道0 - PA0
    sConfig.Channel = ADC_CHANNEL_0;
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_84CYCLES;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道1 - PA1
    sConfig.Channel = ADC_CHANNEL_1;
    sConfig.Rank = 2;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道10 - PC0 (替代PA2避免UART2冲突)
    sConfig.Channel = ADC_CHANNEL_10;
    sConfig.Rank = 3;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道11 - PC1 (替代PA3避免UART2冲突)
    sConfig.Channel = ADC_CHANNEL_11;
    sConfig.Rank = 4;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道12 - PC2 (替代PA4避免SPI冲突)
    sConfig.Channel = ADC_CHANNEL_12;
    sConfig.Rank = 5;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道13 - PC3 (替代PA5避免SPI冲突)
    sConfig.Channel = ADC_CHANNEL_13;
    sConfig.Rank = 6;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道8 - PB0 (替代PA6避免SPI冲突)
    sConfig.Channel = ADC_CHANNEL_8;
    sConfig.Rank = 7;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    // 通道9 - PB1 (替代PA7避免SPI冲突)
    sConfig.Channel = ADC_CHANNEL_9;
    sConfig.Rank = 8;
    if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief ADC MSP初始化
 */
void HAL_ADC_MspInit(ADC_HandleTypeDef* adcHandle)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    if(adcHandle->Instance==ADC1)
    {
        /* ADC1 clock enable */
        __HAL_RCC_ADC1_CLK_ENABLE();
        
        __HAL_RCC_GPIOA_CLK_ENABLE();
        __HAL_RCC_GPIOB_CLK_ENABLE();
        __HAL_RCC_GPIOC_CLK_ENABLE();
        
        /**ADC1 GPIO Configuration
        八路灰度传感器引脚分配 (避免与现有外设冲突):
        传感器0-1: PA0-PA1 (ADC1_IN0-1)
        传感器2-5: PC0-PC3 (ADC1_IN10-13)
        传感器6-7: PB0-PB1 (ADC1_IN8-9)
        */
        // PA0-PA1 用于传感器0-1
        GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1;
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        // PC0-PC3 用于传感器2-5
        GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

        // PB0-PB1 用于传感器6-7
        GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1;
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
        
        /* ADC1 DMA Init */
        hdma_adc1.Instance = DMA2_Stream0;
        hdma_adc1.Init.Channel = DMA_CHANNEL_0;
        hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY;
        hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;
        hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
        hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
        hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
        hdma_adc1.Init.Mode = DMA_CIRCULAR;
        hdma_adc1.Init.Priority = DMA_PRIORITY_HIGH;
        hdma_adc1.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
        
        if (HAL_DMA_Init(&hdma_adc1) != HAL_OK)
        {
            Error_Handler();
        }

        __HAL_LINKDMA(adcHandle,DMA_Handle,hdma_adc1);
    }
}

/**
 * @brief ADC MSP反初始化
 */
void HAL_ADC_MspDeInit(ADC_HandleTypeDef* adcHandle)
{
    if(adcHandle->Instance==ADC1)
    {
        /* Peripheral clock disable */
        __HAL_RCC_ADC1_CLK_DISABLE();
        
        /**ADC1 GPIO Configuration
        */
        HAL_GPIO_DeInit(GPIOA, GPIO_PIN_0|GPIO_PIN_1);
        HAL_GPIO_DeInit(GPIOC, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3);
        HAL_GPIO_DeInit(GPIOB, GPIO_PIN_0|GPIO_PIN_1);
        
        /* ADC1 DMA DeInit */
        HAL_DMA_DeInit(adcHandle->DMA_Handle);
    }
}

/**
 * @brief 启动ADC DMA转换
 */
void start_adc_conversion(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_buffer, GRAY_SENSOR_COUNT);
}

/**
 * @brief 停止ADC DMA转换
 */
void stop_adc_conversion(void)
{
    HAL_ADC_Stop_DMA(&hadc1);
}

/**
 * @brief ADC转换完成回调函数
 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    if(hadc->Instance == ADC1)
    {
        adc_conversion_complete = 1;
    }
}

/**
 * @brief 获取ADC转换结果
 * @param channel 通道号 (0-7)
 * @return ADC值 (0-4095)
 */
uint16_t get_adc_value(uint8_t channel)
{
    if(channel < GRAY_SENSOR_COUNT)
    {
        return adc_buffer[channel];
    }
    return 0;
}

/**
 * @brief 获取所有ADC转换结果
 * @param values 输出数组指针
 */
void get_all_adc_values(uint16_t *values)
{
    for(uint8_t i = 0; i < GRAY_SENSOR_COUNT; i++)
    {
        values[i] = adc_buffer[i];
    }
}

/**
 * @brief 检查ADC转换是否完成
 * @return 1-完成 0-未完成
 */
uint8_t is_adc_conversion_complete(void)
{
    if(adc_conversion_complete)
    {
        adc_conversion_complete = 0;
        return 1;
    }
    return 0;
}
