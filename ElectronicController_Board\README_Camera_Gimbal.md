# 摄像头云台控制系统

## 概述

本系统基于STM32F4实现了一个智能摄像头云台控制系统，能够接收摄像头发送的矩形检测数据，并通过PID控制算法精确控制云台电机，使摄像头中心点精确瞄准检测到的矩形中心。

## 主要功能

### 1. 摄像头数据解析
- 支持多种数据格式（矩形数据、图像数据、状态数据）
- 完整的数据校验机制（帧头检测、长度校验、校验和验证）
- 实时数据统计和错误处理

### 2. 智能云台控制
- 基于PID算法的精确位置控制
- 根据置信度动态调整控制强度
- 死区处理避免小幅抖动
- 速度自适应调整

### 3. 参数配置和调试
- 支持实时参数调整
- 丰富的调试信息输出
- 配置参数Flash存储

## 硬件连接

### 串口连接
- **USART1 (PA9/PA10)**: 用户命令接口，波特率115200
- **USART2 (PA2/PA3)**: X轴电机控制，波特率115200  
- **USART3 (PD8/PD9)**: 摄像头数据接口，波特率115200
- **UART5**: Y轴电机控制，波特率115200

### 电机连接
- **X轴电机**: 通过USART2控制，地址0x01
- **Y轴电机**: 通过UART5控制，地址0x01

## 软件架构

### 核心文件
- `project_core.c/h`: 主程序核心逻辑
- `camera_control.c/h`: 摄像头控制和数据处理
- `Examples/camera_gimbal_example.c`: 使用示例

### 任务调度
系统采用多任务定时器实现：
- **camera_task (50ms)**: 处理摄像头数据
- **motor_task (20ms)**: 执行自动跟踪控制
- **pid_task (10ms)**: PID精确控制
- **user_task (100ms)**: 处理用户命令
- **oled_task (500ms)**: 显示更新

## 摄像头数据协议

### 数据帧格式
```
+--------+--------+--------+--------+----------+----------+
| Header1| Header2|  CMD   | Length |   Data   | Checksum |
|  0xFF  |  0xA5  |  0x01  |  0x11  | 17 bytes |   XOR    |
+--------+--------+--------+--------+----------+----------+
```

### 矩形数据结构（17字节）
```c
typedef struct __attribute__((packed))
{
    int16_t center_x;     // 矩形中心X坐标
    int16_t center_y;     // 矩形中心Y坐标
    int16_t rect_x;       // 矩形左上角X坐标
    int16_t rect_y;       // 矩形左上角Y坐标
    int16_t width;        // 矩形宽度
    int16_t height;       // 矩形高度
    uint8_t confidence;   // 检测置信度 (0-100)
    uint32_t frame_id;    // 帧ID
    uint8_t checksum;     // 校验和
} RectData_t;
```

### 示例数据包
检测到中心在(160,120)，大小为50x40的矩形：
```
FF A5 01 11 A0 00 78 00 87 00 64 00 32 00 28 00 5A 01 00 00 00 XX
```

## 控制算法

### 1. 误差计算
```c
int16_t error_x = camera_data.center_x - CAM_WIDTH / 2;
int16_t error_y = camera_data.center_y - CAM_HEIGHT / 2;
```

### 2. PID控制
```c
float adjust_x = calculate_pid(&pid_x, error_x, 0) * confidence_factor;
float adjust_y = calculate_pid(&pid_y, error_y, 0) * confidence_factor;
```

### 3. 步数转换
```c
int32_t step_x = (int32_t)(adjust_x * gimbal_control.pixel_to_step_x);
int32_t step_y = (int32_t)(adjust_y * gimbal_control.pixel_to_step_y);
```

## 串口命令

### 基本控制命令
- `$A1`: 启用自动跟踪
- `$A0`: 禁用自动跟踪
- `$H`: 回到原点位置
- `$S`: 保存当前位置为原点

### 手动控制命令
- `$M[x_offset][y_offset]`: 手动调整位置

### 调试命令
- `$D1`: 显示摄像头状态
- `$D2`: 显示电机状态

### 摄像头控制命令
- `$C[cmd][param]`: 发送控制命令到摄像头

## 参数配置

### 云台控制参数
```c
gimbal_control.pixel_to_step_x = 8.0f;    // X轴像素到步数转换比例
gimbal_control.pixel_to_step_y = 8.0f;    // Y轴像素到步数转换比例
gimbal_control.dead_zone_x = 3;           // X轴死区（像素）
gimbal_control.dead_zone_y = 3;           // Y轴死区（像素）
gimbal_control.max_step_per_cycle = 800;  // 单次最大调整步数
gimbal_control.confidence_threshold = 40; // 置信度阈值40%
```

### PID参数
```c
#define PID_KP 1.5f         // PID比例系数
#define PID_KI 0.05f        // PID积分系数
#define PID_KD 0.2f         // PID微分系数
#define PID_MAX_OUTPUT 5000 // PID最大输出
```

## 使用步骤

### 1. 硬件准备
1. 连接STM32开发板和电机驱动器
2. 连接摄像头到USART3
3. 连接调试串口到USART1

### 2. 软件配置
1. 编译并下载程序到STM32
2. 通过串口调试工具连接USART1
3. 发送`$A1`启用自动跟踪

### 3. 参数调优
1. 根据实际硬件调整`pixel_to_step_x/y`参数
2. 调整PID参数以获得最佳响应特性
3. 设置合适的置信度阈值

### 4. 运行测试
1. 在摄像头视野中放置矩形目标
2. 观察云台是否能准确跟踪目标
3. 通过调试命令监控系统状态

## 故障排除

### 常见问题
1. **云台不响应**: 检查电机连接和地址设置
2. **跟踪不准确**: 调整像素到步数转换比例
3. **系统抖动**: 增大死区或调整PID参数
4. **数据丢失**: 检查串口连接和波特率设置

### 调试方法
1. 使用`$D1`和`$D2`命令查看系统状态
2. 监控串口输出的错误信息
3. 检查摄像头数据格式是否正确
4. 验证电机响应是否正常

## 扩展功能

### 可添加的功能
1. 多目标跟踪和优先级选择
2. 目标预测和前馈控制
3. 图像稳定和防抖功能
4. 远程控制和监控界面

### 性能优化
1. 使用DMA传输提高效率
2. 实现卡尔曼滤波减少噪声
3. 添加自适应控制算法
4. 优化内存使用和任务调度

## 技术支持

如有问题请联系：米醋电子工作室

---
*本文档版本: v1.0*  
*最后更新: 2024年*
