/**
 * @file main.c
 * @brief MSPM0G3507八路灰度传感器循迹主程序
 * <AUTHOR>
 * @date 2024
 */

#include "ti_msp_dl_config.h"
#include "line_following.h"
#include "adc_config.h"
#include <stdio.h>
#include <string.h>

/* 串口发送函数 */
void uart_send_string(char* str);
void uart_send_char(char ch);

/* 命令处理函数 */
void process_uart_commands(void);

/* 全局变量 */
static char uart_rx_buffer[64];
static uint8_t uart_rx_index = 0;
static bool uart_cmd_ready = false;

int main(void)
{
    char output_buffer[128];
    uint32_t last_debug_time = 0;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化循迹系统
    line_following_init();
    
    uart_send_string("MSPM0G3507 Line Following System Started\r\n");
    uart_send_string("Commands:\r\n");
    uart_send_string("  L1 - Start line following\r\n");
    uart_send_string("  L0 - Stop line following\r\n");
    uart_send_string("  LS - Show sensor status\r\n");
    uart_send_string("  LC - Calibrate sensors\r\n");
    uart_send_string("  LD - Toggle debug mode\r\n");
    uart_send_string("Ready!\r\n\r\n");
    
    while (1)
    {
        // 处理串口命令
        process_uart_commands();
        
        // 执行循迹控制
        line_following_task();
        
        // 调试信息输出 (每500ms)
        if(line_config.debug_mode && 
           (get_system_time_ms() - last_debug_time) > 500)
        {
            sprintf(output_buffer, 
                   "Sensors: %d%d%d%d%d%d%d%d | Pos: %.2f | State: %d | L: %d R: %d\r\n",
                   line_sensor.digital_values[0], line_sensor.digital_values[1],
                   line_sensor.digital_values[2], line_sensor.digital_values[3],
                   line_sensor.digital_values[4], line_sensor.digital_values[5],
                   line_sensor.digital_values[6], line_sensor.digital_values[7],
                   line_sensor.line_position, line_sensor.state,
                   motor_control.left_speed, motor_control.right_speed);
            uart_send_string(output_buffer);
            
            last_debug_time = get_system_time_ms();
        }
        
        // 短暂延时
        delay_cycles(32000); // 约1ms
    }
}

/**
 * @brief 串口发送字符串
 */
void uart_send_string(char* str)
{
    while(*str != 0 && str != 0)
    {
        while(DL_UART_isBusy(UART_0_INST) == true);
        DL_UART_Main_transmitData(UART_0_INST, *str++);
    }
}

/**
 * @brief 串口发送字符
 */
void uart_send_char(char ch)
{
    while(DL_UART_isBusy(UART_0_INST) == true);
    DL_UART_Main_transmitData(UART_0_INST, ch);
}

/**
 * @brief 处理串口命令
 */
void process_uart_commands(void)
{
    if(!uart_cmd_ready)
        return;
    
    char output_buffer[256];
    
    if(strncmp(uart_rx_buffer, "L1", 2) == 0)
    {
        // 启动循迹
        line_config.enabled = true;
        uart_send_string("Line following started\r\n");
    }
    else if(strncmp(uart_rx_buffer, "L0", 2) == 0)
    {
        // 停止循迹
        line_config.enabled = false;
        stop_motors();
        uart_send_string("Line following stopped\r\n");
    }
    else if(strncmp(uart_rx_buffer, "LS", 2) == 0)
    {
        // 显示传感器状态
        sprintf(output_buffer,
               "Sensor Status:\r\n"
               "Raw Values: %d %d %d %d %d %d %d %d\r\n"
               "Digital: %d%d%d%d%d%d%d%d\r\n"
               "Threshold: %d\r\n"
               "Line Position: %.2f\r\n"
               "Sensors on Line: %d\r\n"
               "State: %d\r\n",
               line_sensor.raw_values[0], line_sensor.raw_values[1],
               line_sensor.raw_values[2], line_sensor.raw_values[3],
               line_sensor.raw_values[4], line_sensor.raw_values[5],
               line_sensor.raw_values[6], line_sensor.raw_values[7],
               line_sensor.digital_values[0], line_sensor.digital_values[1],
               line_sensor.digital_values[2], line_sensor.digital_values[3],
               line_sensor.digital_values[4], line_sensor.digital_values[5],
               line_sensor.digital_values[6], line_sensor.digital_values[7],
               line_sensor.threshold, line_sensor.line_position,
               line_sensor.sensors_on_line, line_sensor.state);
        uart_send_string(output_buffer);
    }
    else if(strncmp(uart_rx_buffer, "LC", 2) == 0)
    {
        // 校准传感器
        line_config.enabled = false;
        stop_motors();
        uart_send_string("Starting calibration... Move robot over black and white areas\r\n");
        calibrate_line_sensors(5000); // 5秒校准
        sprintf(output_buffer, "Calibration complete. New threshold: %d\r\n", line_sensor.threshold);
        uart_send_string(output_buffer);
    }
    else if(strncmp(uart_rx_buffer, "LD", 2) == 0)
    {
        // 切换调试模式
        line_config.debug_mode = !line_config.debug_mode;
        sprintf(output_buffer, "Debug mode: %s\r\n", line_config.debug_mode ? "ON" : "OFF");
        uart_send_string(output_buffer);
    }
    else
    {
        uart_send_string("Unknown command\r\n");
    }
    
    // 清除命令缓冲区
    memset(uart_rx_buffer, 0, sizeof(uart_rx_buffer));
    uart_rx_index = 0;
    uart_cmd_ready = false;
}

/**
 * @brief UART中断回调函数
 */
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST))
    {
        case DL_UART_MAIN_IIDX_RX:
        {
            uint8_t received_char = DL_UART_Main_receiveData(UART_0_INST);
            
            if(received_char == '\r' || received_char == '\n')
            {
                if(uart_rx_index > 0)
                {
                    uart_rx_buffer[uart_rx_index] = '\0';
                    uart_cmd_ready = true;
                }
            }
            else if(uart_rx_index < (sizeof(uart_rx_buffer) - 1))
            {
                uart_rx_buffer[uart_rx_index++] = received_char;
                uart_send_char(received_char); // 回显
            }
            break;
        }
        default:
            break;
    }
}
