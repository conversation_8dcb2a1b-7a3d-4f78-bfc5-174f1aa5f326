/**
 * @file sensor_type_test.c
 * @brief 传感器类型检测测试程序
 * <AUTHOR>
 * @date 2024
 */

#include "ti_msp_dl_config.h"
#include <stdio.h>

/* 引脚定义 */
#define SENSOR_OUT_PORT     GPIOA
#define SENSOR_OUT_PIN      DL_GPIO_PIN_1    // PA1 (A01)
#define SENSOR_EN_PORT      GPIOA
#define SENSOR_EN_PIN       DL_GPIO_PIN_26   // PA26
#define SENSOR_AD2_PORT     GPIOB
#define SENSOR_AD2_PIN      DL_GPIO_PIN_21   // PB21 (B21)
#define SENSOR_AD1_PORT     GPIOA
#define SENSOR_AD1_PIN      DL_GPIO_PIN_9    // PA9 (A09)
#define SENSOR_AD0_PORT     GPIOB
#define SENSOR_AD0_PIN      DL_GPIO_PIN_6    // PB6 (B06)
#define SENSOR_ERR_PORT     GPIOA
#define SENSOR_ERR_PIN      DL_GPIO_PIN_16   // PA16

void uart_send_string(char* str);
void delay_us(uint32_t us);
void test_sensor_type(void);
void test_digital_mode(void);
void test_analog_mode(void);

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    uart_send_string("Sensor Type Detection Test\r\n");
    uart_send_string("==========================\r\n");
    
    // 初始化GPIO
    // EN引脚配置为输出
    DL_GPIO_initDigitalOutput(SENSOR_EN_PIN);
    DL_GPIO_clearPins(SENSOR_EN_PORT, SENSOR_EN_PIN);
    
    // 地址引脚配置为输出
    DL_GPIO_initDigitalOutput(SENSOR_AD2_PIN);
    DL_GPIO_initDigitalOutput(SENSOR_AD1_PIN);
    DL_GPIO_initDigitalOutput(SENSOR_AD0_PIN);
    DL_GPIO_clearPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
    DL_GPIO_clearPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
    DL_GPIO_clearPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
    
    // ERR引脚配置为输入
    DL_GPIO_initDigitalInput(SENSOR_ERR_PIN);
    
    uart_send_string("GPIO initialized\r\n\r\n");
    
    // 开始测试
    test_sensor_type();
    
    while(1)
    {
        // 主循环
        delay_us(1000000); // 1秒延时
    }
}

/**
 * @brief 测试传感器类型
 */
void test_sensor_type(void)
{
    uart_send_string("Testing sensor type...\r\n");
    
    // 使能传感器
    DL_GPIO_setPins(SENSOR_EN_PORT, SENSOR_EN_PIN);
    delay_us(1000);
    
    uart_send_string("Sensor enabled\r\n");
    
    // 测试数字模式
    uart_send_string("\n--- Digital Mode Test ---\r\n");
    test_digital_mode();
    
    // 测试模拟模式
    uart_send_string("\n--- Analog Mode Test ---\r\n");
    test_analog_mode();
    
    uart_send_string("\nTest completed!\r\n");
    uart_send_string("Please check the output to determine sensor type.\r\n");
}

/**
 * @brief 测试数字模式
 */
void test_digital_mode(void)
{
    char output_buffer[128];
    
    // 配置OUT引脚为数字输入
    DL_GPIO_initDigitalInput(SENSOR_OUT_PIN);
    DL_GPIO_setPins(SENSOR_OUT_PORT, SENSOR_OUT_PIN); // 上拉
    
    uart_send_string("OUT pin configured as digital input\r\n");
    
    // 测试不同地址
    for(uint8_t addr = 0; addr < 8; addr++)
    {
        // 设置地址
        if(addr & 0x01) DL_GPIO_setPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
        else DL_GPIO_clearPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
        
        if(addr & 0x02) DL_GPIO_setPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
        else DL_GPIO_clearPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
        
        if(addr & 0x04) DL_GPIO_setPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
        else DL_GPIO_clearPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
        
        delay_us(100); // 等待稳定
        
        // 读取数字值
        uint32_t digital_value = DL_GPIO_readPins(SENSOR_OUT_PORT, SENSOR_OUT_PIN);
        
        sprintf(output_buffer, "Address %d: Digital = %s\r\n", 
                addr, digital_value ? "HIGH" : "LOW");
        uart_send_string(output_buffer);
    }
}

/**
 * @brief 测试模拟模式
 */
void test_analog_mode(void)
{
    char output_buffer[128];
    
    uart_send_string("Testing analog mode (simulated)...\r\n");
    uart_send_string("Note: This requires ADC configuration in SysConfig\r\n");
    
    // 这里只是示例，实际需要配置ADC
    for(uint8_t addr = 0; addr < 8; addr++)
    {
        // 设置地址
        if(addr & 0x01) DL_GPIO_setPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
        else DL_GPIO_clearPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
        
        if(addr & 0x02) DL_GPIO_setPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
        else DL_GPIO_clearPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
        
        if(addr & 0x04) DL_GPIO_setPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
        else DL_GPIO_clearPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
        
        delay_us(100);
        
        // 这里需要实际的ADC读取代码
        // uint16_t adc_value = read_adc_channel();
        
        sprintf(output_buffer, "Address %d: ADC = (需要ADC配置)\r\n", addr);
        uart_send_string(output_buffer);
    }
}

/**
 * @brief 串口发送字符串
 */
void uart_send_string(char* str)
{
    while(*str != 0)
    {
        while(DL_UART_isBusy(UART_0_INST) == true);
        DL_UART_Main_transmitData(UART_0_INST, *str++);
    }
}

/**
 * @brief 微秒延时
 */
void delay_us(uint32_t us)
{
    uint32_t cycles = us * 32; // 假设32MHz时钟
    for(uint32_t i = 0; i < cycles; i++)
    {
        __asm("nop");
    }
}
