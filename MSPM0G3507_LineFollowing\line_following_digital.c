/**
 * @file line_following_digital.c
 * @brief MSPM0G3507数字式八路灰度传感器循迹控制系统实现
 * <AUTHOR>
 * @date 2024
 */

#include "line_following_digital.h"
#include "ti/driverlib/dl_timer.h"
#include "ti/driverlib/dl_gpio.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

/* 全局变量定义 */
LinePID_t line_pid;
MotorControl_t motor_control;
LineFollowConfig_t line_config;
LineFollowStats_t line_stats;
LineState_t current_line_state;

/* 系统时间计数器 */
static volatile uint32_t system_time_ms = 0;

/**
 * @brief 初始化数字式循迹系统
 */
void digital_line_following_init(void)
{
    // 初始化数字传感器
    digital_line_sensor_init();
    
    // 初始化PID控制器
    line_pid.kp = LINE_PID_KP;
    line_pid.ki = LINE_PID_KI;
    line_pid.kd = LINE_PID_KD;
    line_pid.max_output = LINE_PID_MAX_OUTPUT;
    line_pid.error = 0;
    line_pid.last_error = 0;
    line_pid.integral = 0;
    line_pid.derivative = 0;
    line_pid.output = 0;
    
    // 初始化电机控制
    motor_control.base_speed = MOTOR_BASE_SPEED;
    motor_control.left_speed = 0;
    motor_control.right_speed = 0;
    motor_control.direction = 0;
    
    // 初始化配置
    line_config.enabled = false;
    line_config.debug_mode = false;
    line_config.lost_timeout_ms = 1000;
    line_config.turn_delay_ms = 300;  // 数字传感器需要更长的转弯延时
    line_config.speed_factor = 1.0f;
    line_config.sensor_read_interval_ms = 20;  // 20ms读取间隔
    
    // 初始化统计信息
    memset(&line_stats, 0, sizeof(LineFollowStats_t));
    
    // 初始状态
    current_line_state = LINE_STATE_STOP;
    
    // 停止电机
    stop_motors();
}

/**
 * @brief 检测循迹状态
 * @return 当前状态
 */
LineState_t detect_digital_line_state(void)
{
    uint32_t current_time = get_system_time_ms();
    
    // 检查传感器错误
    if(digital_line_sensor.error_flag)
    {
        current_line_state = LINE_STATE_ERROR;
        line_stats.error_count++;
        return current_line_state;
    }
    
    // 检查是否丢线
    if(digital_line_sensor.sensors_on_line == 0)
    {
        if((current_time - digital_line_sensor.last_read_time) > line_config.lost_timeout_ms)
        {
            current_line_state = LINE_STATE_LOST;
            line_stats.line_lost_count++;
            return current_line_state;
        }
    }
    
    // 根据检测到的传感器数量和位置判断状态
    if(digital_line_sensor.sensors_on_line >= 6)
    {
        // 大部分传感器检测到黑线 - 可能是十字路口或终点
        current_line_state = LINE_STATE_INTERSECTION;
        line_stats.intersection_count++;
    }
    else if(digital_line_sensor.sensors_on_line >= 1 && digital_line_sensor.sensors_on_line <= 4)
    {
        // 正常循迹状态
        if(digital_line_sensor.line_position < -2.5f)
        {
            current_line_state = LINE_STATE_SHARP_LEFT;
            line_stats.turn_count++;
        }
        else if(digital_line_sensor.line_position > 2.5f)
        {
            current_line_state = LINE_STATE_SHARP_RIGHT;
            line_stats.turn_count++;
        }
        else if(digital_line_sensor.line_position < -1.5f)
        {
            current_line_state = LINE_STATE_LEFT_TURN;
        }
        else if(digital_line_sensor.line_position > 1.5f)
        {
            current_line_state = LINE_STATE_RIGHT_TURN;
        }
        else
        {
            current_line_state = LINE_STATE_NORMAL;
        }
    }
    else if(digital_line_sensor.sensors_on_line == 0)
    {
        // 暂时丢线，保持上次状态
        // current_line_state 保持不变
    }
    
    return current_line_state;
}

/**
 * @brief PID控制计算
 * @param setpoint 设定值 (通常为0)
 * @param measured_value 测量值 (黑线位置)
 * @return PID输出
 */
float digital_line_pid_calculate(float setpoint, float measured_value)
{
    line_pid.error = setpoint - measured_value;
    
    // 积分项
    line_pid.integral += line_pid.error;
    
    // 积分限幅
    float max_integral = line_pid.max_output / line_pid.ki;
    if(line_pid.integral > max_integral)
        line_pid.integral = max_integral;
    else if(line_pid.integral < -max_integral)
        line_pid.integral = -max_integral;
    
    // 微分项
    line_pid.derivative = line_pid.error - line_pid.last_error;
    
    // PID输出计算
    line_pid.output = line_pid.kp * line_pid.error + 
                      line_pid.ki * line_pid.integral + 
                      line_pid.kd * line_pid.derivative;
    
    // 输出限幅
    if(line_pid.output > line_pid.max_output)
        line_pid.output = line_pid.max_output;
    else if(line_pid.output < -line_pid.max_output)
        line_pid.output = -line_pid.max_output;
    
    line_pid.last_error = line_pid.error;
    
    return line_pid.output;
}

/**
 * @brief 电机控制
 * @param pid_output PID输出值
 */
void digital_control_motors(float pid_output)
{
    // 基础速度调整
    int16_t base_speed = (int16_t)(motor_control.base_speed * line_config.speed_factor);
    
    // 计算左右电机速度
    motor_control.left_speed = base_speed - (int16_t)pid_output;
    motor_control.right_speed = base_speed + (int16_t)pid_output;
    
    // 速度限制
    if(motor_control.left_speed > MOTOR_MAX_SPEED)
        motor_control.left_speed = MOTOR_MAX_SPEED;
    else if(motor_control.left_speed < -MOTOR_MAX_SPEED)
        motor_control.left_speed = -MOTOR_MAX_SPEED;
        
    if(motor_control.right_speed > MOTOR_MAX_SPEED)
        motor_control.right_speed = MOTOR_MAX_SPEED;
    else if(motor_control.right_speed < -MOTOR_MAX_SPEED)
        motor_control.right_speed = -MOTOR_MAX_SPEED;
    
    // 发送电机控制命令
    set_motor_speeds(motor_control.left_speed, motor_control.right_speed);
}

/**
 * @brief 处理特殊状态 (转弯、路口等)
 */
void handle_digital_special_states(void)
{
    static uint32_t state_start_time = 0;
    uint32_t current_time = get_system_time_ms();
    
    switch(current_line_state)
    {
        case LINE_STATE_LOST:
            // 丢线处理 - 停止
            stop_motors();
            break;
            
        case LINE_STATE_ERROR:
            // 传感器错误 - 停止并重新初始化
            stop_motors();
            digital_line_sensor_init();
            break;
            
        case LINE_STATE_INTERSECTION:
            // 十字路口处理 - 直行通过
            if(state_start_time == 0)
            {
                state_start_time = current_time;
            }
            
            // 直行一段时间后恢复正常循迹
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                current_line_state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            else
            {
                // 保持直行
                set_motor_speeds(motor_control.base_speed, motor_control.base_speed);
            }
            break;
            
        case LINE_STATE_SHARP_LEFT:
            // 急左转处理
            if(state_start_time == 0)
            {
                state_start_time = current_time;
            }
            
            // 原地左转
            int16_t turn_speed = (int16_t)(motor_control.base_speed * MOTOR_TURN_SPEED_RATIO);
            set_motor_speeds(-turn_speed, turn_speed);
            
            // 转弯延时后恢复PID控制
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                current_line_state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            break;
            
        case LINE_STATE_SHARP_RIGHT:
            // 急右转处理
            if(state_start_time == 0)
            {
                state_start_time = current_time;
            }
            
            // 原地右转
            turn_speed = (int16_t)(motor_control.base_speed * MOTOR_TURN_SPEED_RATIO);
            set_motor_speeds(turn_speed, -turn_speed);
            
            // 转弯延时后恢复PID控制
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                current_line_state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            break;
            
        case LINE_STATE_END:
            // 终点处理
            stop_motors();
            line_config.enabled = false;
            break;
            
        case LINE_STATE_STOP:
            // 停止状态
            stop_motors();
            break;
            
        default:
            // 正常状态和普通转弯由PID控制处理
            state_start_time = 0;
            break;
    }
}
