/**
 * @file camera_gimbal_example.c
 * @brief 摄像头云台控制示例程序
 * <AUTHOR>
 * @date 2024
 * 
 * 本示例展示如何使用改进的摄像头数据解析和云台控制功能
 * 实现摄像头检测矩形目标并控制云台精确瞄准的功能
 */

#include "camera_control.h"
#include "project_core.h"
#include "uart.h"
#include <stdio.h>

/**
 * @brief 摄像头云台控制示例主函数
 * 
 * 使用说明：
 * 1. 摄像头通过USART3发送矩形检测数据
 * 2. STM32解析数据并控制X/Y轴电机调整云台位置
 * 3. 通过PID控制实现精确瞄准矩形中心
 * 4. 支持通过串口命令调试和配置参数
 */
void camera_gimbal_example(void)
{
    // 1. 系统初始化（在main函数中已完成）
    // system_init();
    // start_all_tasks();
    
    // 2. 设置摄像头参数
    uart_print("Setting camera parameters...\r\n");
    
    // 设置二值化阈值
    if (set_camera_threshold(128) == 0)
    {
        uart_print("Camera threshold set to 128\r\n");
    }
    else
    {
        uart_print("Failed to set camera threshold\r\n");
    }
    
    // 设置感兴趣区域（ROI）- 中心区域
    int16_t roi_x = CAM_WIDTH / 4;
    int16_t roi_y = CAM_HEIGHT / 4;
    int16_t roi_width = CAM_WIDTH / 2;
    int16_t roi_height = CAM_HEIGHT / 2;
    
    if (set_camera_roi(roi_x, roi_y, roi_width, roi_height) == 0)
    {
        uart_print("Camera ROI set to (%d, %d, %d, %d)\r\n", 
                  roi_x, roi_y, roi_width, roi_height);
    }
    else
    {
        uart_print("Failed to set camera ROI\r\n");
    }
    
    // 3. 配置云台控制参数
    uart_print("Configuring gimbal control parameters...\r\n");
    
    // 根据实际硬件调整这些参数
    gimbal_control.pixel_to_step_x = 8.0f;  // X轴像素到步数转换比例
    gimbal_control.pixel_to_step_y = 8.0f;  // Y轴像素到步数转换比例
    gimbal_control.dead_zone_x = 3;         // X轴死区（像素）
    gimbal_control.dead_zone_y = 3;         // Y轴死区（像素）
    gimbal_control.max_step_per_cycle = 800; // 单次最大调整步数
    gimbal_control.confidence_threshold = 40; // 置信度阈值40%
    
    uart_print("Gimbal control parameters configured\r\n");
    
    // 4. 启用自动跟踪
    sys_config.auto_track = 1;
    uart_print("Auto tracking enabled\r\n");
    
    // 5. 主循环（实际上由多任务定时器处理）
    uart_print("Camera gimbal control system started\r\n");
    uart_print("Send commands:\r\n");
    uart_print("  $A1 - Enable auto tracking\r\n");
    uart_print("  $A0 - Disable auto tracking\r\n");
    uart_print("  $D1 - Show camera status\r\n");
    uart_print("  $D2 - Show motor status\r\n");
    uart_print("  $H  - Return to home position\r\n");
    uart_print("  $S  - Save current position as home\r\n");
    
    // 系统现在会自动运行，通过以下任务处理：
    // - camera_task(): 处理摄像头数据
    // - motor_task(): 执行自动跟踪控制
    // - pid_task(): PID精确控制
    // - user_task(): 处理用户命令
}

/**
 * @brief 摄像头数据格式说明
 * 
 * 摄像头应发送以下格式的数据包：
 * 
 * 矩形检测数据包格式：
 * +--------+--------+--------+--------+----------+----------+----------+----------+
 * | Header1| Header2|  CMD   | Length |  Data (Length bytes)  | Checksum |
 * |  0xFF  |  0xA5  |  0x01  |  0x11  |    17 bytes data      |   XOR    |
 * +--------+--------+--------+--------+----------+----------+----------+----------+
 * 
 * 数据部分（17字节）：
 * - center_x (2字节): 矩形中心X坐标，小端字节序
 * - center_y (2字节): 矩形中心Y坐标，小端字节序  
 * - rect_x (2字节): 矩形左上角X坐标，小端字节序
 * - rect_y (2字节): 矩形左上角Y坐标，小端字节序
 * - width (2字节): 矩形宽度，小端字节序
 * - height (2字节): 矩形高度，小端字节序
 * - confidence (1字节): 检测置信度 (0-100)
 * - frame_id (4字节): 帧ID，小端字节序
 * - checksum (1字节): 数据校验和（XOR）
 * 
 * 示例数据包（检测到中心在(160,120)，大小为50x40的矩形）：
 * FF A5 01 11 A0 00 78 00 87 00 64 00 32 00 28 00 5A 01 00 00 00 XX
 * 
 * 其中：
 * - A0 00 = 160 (center_x)
 * - 78 00 = 120 (center_y)  
 * - 87 00 = 135 (rect_x = center_x - width/2)
 * - 64 00 = 100 (rect_y = center_y - height/2)
 * - 32 00 = 50 (width)
 * - 28 00 = 40 (height)
 * - 5A = 90 (confidence)
 * - 01 00 00 00 = 1 (frame_id)
 * - XX = 校验和
 */

/**
 * @brief 云台控制原理说明
 * 
 * 1. 目标检测：
 *    - 摄像头检测图像中的矩形目标
 *    - 计算矩形的中心坐标和边界框信息
 *    - 评估检测的置信度
 * 
 * 2. 误差计算：
 *    - 计算目标中心与图像中心的偏差（像素）
 *    - error_x = target_center_x - image_center_x
 *    - error_y = target_center_y - image_center_y
 * 
 * 3. 控制策略：
 *    - 使用PID控制器计算调整量
 *    - 根据置信度调整控制强度
 *    - 应用死区避免小幅抖动
 *    - 限制单次调整幅度防止过冲
 * 
 * 4. 电机控制：
 *    - 将像素误差转换为电机步数
 *    - 根据误差大小动态调整电机速度
 *    - 发送绝对定位命令到步进电机
 * 
 * 5. 参数调优：
 *    - pixel_to_step_x/y: 根据摄像头视野角度和电机分辨率调整
 *    - PID参数: 根据系统响应特性调整
 *    - dead_zone: 根据系统稳定性要求调整
 *    - confidence_threshold: 根据检测精度要求调整
 */

/**
 * @brief 调试和监控功能
 * 
 * 系统提供了丰富的调试和监控功能：
 * 
 * 1. 实时状态监控：
 *    - 发送 "$D1" 查看摄像头状态
 *    - 发送 "$D2" 查看电机状态
 * 
 * 2. 参数调整：
 *    - 可通过串口命令动态调整控制参数
 *    - 支持保存参数到Flash存储器
 * 
 * 3. 统计信息：
 *    - 跟踪帧处理成功率
 *    - 监控目标检测率
 *    - 记录平均置信度
 * 
 * 4. 错误处理：
 *    - 数据校验失败处理
 *    - 通信超时处理
 *    - 电机限位保护
 */

/**
 * @brief 性能优化建议
 * 
 * 1. 通信优化：
 *    - 使用DMA传输减少CPU占用
 *    - 合理设置串口波特率
 *    - 实现数据缓冲和批处理
 * 
 * 2. 控制优化：
 *    - 根据目标大小调整控制参数
 *    - 实现预测控制算法
 *    - 添加卡尔曼滤波减少噪声
 * 
 * 3. 系统优化：
 *    - 合理分配任务优先级
 *    - 优化内存使用
 *    - 实现看门狗保护
 */
