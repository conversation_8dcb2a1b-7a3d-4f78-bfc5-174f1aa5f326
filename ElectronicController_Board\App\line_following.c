/**
 * @file line_following.c
 * @brief 八路灰度传感器循迹控制系统实现
 * <AUTHOR>
 * @date 2024
 */

#include "line_following.h"
#include "uart.h"
#include "emm_v5.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

/* 全局变量定义 */
LineSensor_t line_sensor;
LinePID_t line_pid;
MotorControl_t motor_control;
LineFollowConfig_t line_config;
LineFollowStats_t line_stats;

/* 校准数据 */
static uint16_t sensor_min[LINE_SENSOR_COUNT];
static uint16_t sensor_max[LINE_SENSOR_COUNT];
static uint8_t calibration_done = 0;

/* 内部函数声明 */
static const char* get_state_name(LineState_t state);

/**
 * @brief 初始化循迹系统
 */
void line_following_init(void)
{
    // 初始化传感器数据
    memset(&line_sensor, 0, sizeof(LineSensor_t));
    line_sensor.threshold = LINE_THRESHOLD_DEFAULT;
    line_sensor.state = LINE_STATE_STOP;
    
    // 初始化PID控制器
    line_pid.kp = LINE_PID_KP;
    line_pid.ki = LINE_PID_KI;
    line_pid.kd = LINE_PID_KD;
    line_pid.max_output = LINE_PID_MAX_OUTPUT;
    line_pid.error = 0;
    line_pid.last_error = 0;
    line_pid.integral = 0;
    line_pid.derivative = 0;
    line_pid.output = 0;
    
    // 初始化电机控制
    motor_control.base_speed = MOTOR_BASE_SPEED;
    motor_control.left_speed = 0;
    motor_control.right_speed = 0;
    motor_control.direction = 0;
    
    // 初始化配置
    line_config.enabled = 0;
    line_config.debug_mode = 0;
    line_config.lost_timeout_ms = 1000;
    line_config.turn_delay_ms = 200;
    line_config.auto_calibration = 1;
    line_config.speed_factor = 1.0f;
    
    // 初始化统计信息
    memset(&line_stats, 0, sizeof(LineFollowStats_t));
    
    // 初始化校准数据
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        sensor_min[i] = 4095;
        sensor_max[i] = 0;
    }
    
    uart_print("Line following system initialized\r\n");
}

/**
 * @brief 读取传感器数据
 */
void read_line_sensors(void)
{
    // 获取ADC原始值
    get_all_adc_values(line_sensor.raw_values);
    
    // 数字化处理
    line_sensor.sensors_on_line = 0;
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        if(line_sensor.raw_values[i] > line_sensor.threshold)
        {
            line_sensor.digital_values[i] = 1;  // 检测到黑线
            line_sensor.sensors_on_line++;
        }
        else
        {
            line_sensor.digital_values[i] = 0;  // 白色区域
        }
    }
    
    // 更新最后检测时间
    if(line_sensor.sensors_on_line > 0)
    {
        line_sensor.last_seen_time = HAL_GetTick();
    }
}

/**
 * @brief 校准传感器
 * @param calibration_time_ms 校准时间(毫秒)
 */
void calibrate_line_sensors(uint16_t calibration_time_ms)
{
    uint32_t start_time = HAL_GetTick();
    uint16_t temp_values[LINE_SENSOR_COUNT];
    
    uart_print("Starting sensor calibration for %d ms...\r\n", calibration_time_ms);
    uart_print("Please move the robot over white and black areas\r\n");
    
    // 重置校准数据
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        sensor_min[i] = 4095;
        sensor_max[i] = 0;
    }
    
    // 校准过程
    while((HAL_GetTick() - start_time) < calibration_time_ms)
    {
        get_all_adc_values(temp_values);
        
        for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
        {
            if(temp_values[i] < sensor_min[i])
                sensor_min[i] = temp_values[i];
            if(temp_values[i] > sensor_max[i])
                sensor_max[i] = temp_values[i];
        }
        
        HAL_Delay(10);
    }
    
    // 计算阈值 (最大值和最小值的中间值)
    uint32_t threshold_sum = 0;
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        threshold_sum += (sensor_max[i] + sensor_min[i]) / 2;
    }
    line_sensor.threshold = threshold_sum / LINE_SENSOR_COUNT;
    
    calibration_done = 1;
    
    uart_print("Calibration complete. Threshold: %d\r\n", line_sensor.threshold);
    uart_print("Min values: ");
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        uart_print("%d ", sensor_min[i]);
    }
    uart_print("\r\nMax values: ");
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        uart_print("%d ", sensor_max[i]);
    }
    uart_print("\r\n");
}

/**
 * @brief 计算黑线位置
 * @return 黑线位置 (-3.5 到 +3.5)
 */
float calculate_line_position(void)
{
    uint32_t weighted_sum = 0;
    uint32_t total_weight = 0;
    
    // 加权平均算法计算黑线位置
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        if(line_sensor.digital_values[i])
        {
            // 传感器位置: 0->-3.5, 1->-2.5, ..., 7->+3.5
            int16_t position = (i * 1000) - 3500;  // 乘以1000避免浮点运算
            weighted_sum += position * line_sensor.raw_values[i];
            total_weight += line_sensor.raw_values[i];
        }
    }
    
    if(total_weight > 0)
    {
        line_sensor.line_position = (float)weighted_sum / (float)total_weight / 1000.0f;
    }
    else
    {
        // 没有检测到黑线，保持上次位置
        // line_sensor.line_position 保持不变
    }
    
    return line_sensor.line_position;
}

/**
 * @brief 检测循迹状态
 * @return 当前状态
 */
LineState_t detect_line_state(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 检查是否丢线
    if(line_sensor.sensors_on_line == 0)
    {
        if((current_time - line_sensor.last_seen_time) > line_config.lost_timeout_ms)
        {
            line_sensor.state = LINE_STATE_LOST;
            return line_sensor.state;
        }
    }
    
    // 根据检测到的传感器数量和位置判断状态
    if(line_sensor.sensors_on_line >= 6)
    {
        // 大部分传感器检测到黑线 - 可能是十字路口或终点
        line_sensor.state = LINE_STATE_INTERSECTION;
    }
    else if(line_sensor.sensors_on_line >= 1 && line_sensor.sensors_on_line <= 3)
    {
        // 正常循迹状态
        if(line_sensor.line_position < -2.0f)
        {
            line_sensor.state = LINE_STATE_SHARP_LEFT;
        }
        else if(line_sensor.line_position > 2.0f)
        {
            line_sensor.state = LINE_STATE_SHARP_RIGHT;
        }
        else if(line_sensor.line_position < -1.0f)
        {
            line_sensor.state = LINE_STATE_LEFT_TURN;
        }
        else if(line_sensor.line_position > 1.0f)
        {
            line_sensor.state = LINE_STATE_RIGHT_TURN;
        }
        else
        {
            line_sensor.state = LINE_STATE_NORMAL;
        }
    }
    else if(line_sensor.sensors_on_line == 0)
    {
        // 暂时丢线，保持上次状态
        // line_sensor.state 保持不变
    }
    
    return line_sensor.state;
}

/**
 * @brief PID控制计算
 * @param setpoint 设定值 (通常为0)
 * @param measured_value 测量值 (黑线位置)
 * @return PID输出
 */
float line_pid_calculate(float setpoint, float measured_value)
{
    line_pid.error = setpoint - measured_value;
    
    // 积分项
    line_pid.integral += line_pid.error;
    
    // 积分限幅
    float max_integral = line_pid.max_output / line_pid.ki;
    if(line_pid.integral > max_integral)
        line_pid.integral = max_integral;
    else if(line_pid.integral < -max_integral)
        line_pid.integral = -max_integral;
    
    // 微分项
    line_pid.derivative = line_pid.error - line_pid.last_error;
    
    // PID输出计算
    line_pid.output = line_pid.kp * line_pid.error + 
                      line_pid.ki * line_pid.integral + 
                      line_pid.kd * line_pid.derivative;
    
    // 输出限幅
    if(line_pid.output > line_pid.max_output)
        line_pid.output = line_pid.max_output;
    else if(line_pid.output < -line_pid.max_output)
        line_pid.output = -line_pid.max_output;
    
    line_pid.last_error = line_pid.error;
    
    return line_pid.output;
}

/**
 * @brief 电机控制
 * @param pid_output PID输出值
 */
void control_motors(float pid_output)
{
    // 基础速度调整
    int16_t base_speed = (int16_t)(motor_control.base_speed * line_config.speed_factor);
    
    // 计算左右电机速度
    motor_control.left_speed = base_speed - (int16_t)pid_output;
    motor_control.right_speed = base_speed + (int16_t)pid_output;
    
    // 速度限制
    if(motor_control.left_speed > MOTOR_MAX_SPEED)
        motor_control.left_speed = MOTOR_MAX_SPEED;
    else if(motor_control.left_speed < -MOTOR_MAX_SPEED)
        motor_control.left_speed = -MOTOR_MAX_SPEED;
        
    if(motor_control.right_speed > MOTOR_MAX_SPEED)
        motor_control.right_speed = MOTOR_MAX_SPEED;
    else if(motor_control.right_speed < -MOTOR_MAX_SPEED)
        motor_control.right_speed = -MOTOR_MAX_SPEED;
    
    // 发送电机控制命令
    set_motor_speeds(motor_control.left_speed, motor_control.right_speed);
}

/**
 * @brief 处理特殊状态 (转弯、路口等)
 */
void handle_special_states(void)
{
    static uint32_t state_start_time = 0;
    uint32_t current_time = HAL_GetTick();

    switch(line_sensor.state)
    {
        case LINE_STATE_LOST:
            // 丢线处理 - 停止或搜索
            stop_motors();
            if(line_config.debug_mode)
            {
                uart_print("Line lost! Stopping...\r\n");
            }
            line_stats.line_lost_count++;
            break;

        case LINE_STATE_INTERSECTION:
            // 十字路口处理 - 直行通过
            if(state_start_time == 0)
            {
                state_start_time = current_time;
                line_stats.intersection_count++;
            }

            // 直行一段时间后恢复正常循迹
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                line_sensor.state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            else
            {
                // 保持直行
                set_motor_speeds(motor_control.base_speed, motor_control.base_speed);
            }
            break;

        case LINE_STATE_SHARP_LEFT:
            // 急左转处理
            if(state_start_time == 0)
            {
                state_start_time = current_time;
                line_stats.turn_count++;
            }

            // 原地左转
            int16_t turn_speed = (int16_t)(motor_control.base_speed * MOTOR_TURN_SPEED_RATIO);
            set_motor_speeds(-turn_speed, turn_speed);

            // 转弯延时后恢复PID控制
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                line_sensor.state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            break;

        case LINE_STATE_SHARP_RIGHT:
            // 急右转处理
            if(state_start_time == 0)
            {
                state_start_time = current_time;
                line_stats.turn_count++;
            }

            // 原地右转
            turn_speed = (int16_t)(motor_control.base_speed * MOTOR_TURN_SPEED_RATIO);
            set_motor_speeds(turn_speed, -turn_speed);

            // 转弯延时后恢复PID控制
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                line_sensor.state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            break;

        case LINE_STATE_END:
            // 终点处理
            stop_motors();
            line_config.enabled = 0;
            uart_print("Reached end point. Stopping.\r\n");
            break;

        case LINE_STATE_STOP:
            // 停止状态
            stop_motors();
            break;

        default:
            // 正常状态和普通转弯由PID控制处理
            state_start_time = 0;
            break;
    }
}

/**
 * @brief 循迹主控制函数
 */
void line_following_control(void)
{
    if(!line_config.enabled)
    {
        stop_motors();
        return;
    }

    // 读取传感器数据
    read_line_sensors();

    // 计算黑线位置
    calculate_line_position();

    // 检测当前状态
    detect_line_state();

    // 更新统计信息
    line_stats.total_cycles++;
    line_stats.last_update_time = HAL_GetTick();

    // 根据状态进行控制
    if(line_sensor.state == LINE_STATE_NORMAL ||
       line_sensor.state == LINE_STATE_LEFT_TURN ||
       line_sensor.state == LINE_STATE_RIGHT_TURN)
    {
        // 正常PID控制
        float pid_output = line_pid_calculate(0.0f, line_sensor.line_position);
        control_motors(pid_output);
    }
    else
    {
        // 特殊状态处理
        handle_special_states();
    }

    // 调试信息输出
    if(line_config.debug_mode && (line_stats.total_cycles % 50 == 0))
    {
        uart_print("Pos: %.2f, State: %d, L: %d, R: %d\r\n",
                  line_sensor.line_position,
                  line_sensor.state,
                  motor_control.left_speed,
                  motor_control.right_speed);
    }
}

/**
 * @brief 停止电机
 */
void stop_motors(void)
{
    motor_control.left_speed = 0;
    motor_control.right_speed = 0;
    set_motor_speeds(0, 0);
}

/**
 * @brief 设置电机速度
 * @param left_speed 左电机速度
 * @param right_speed 右电机速度
 */
void set_motor_speeds(int16_t left_speed, int16_t right_speed)
{
    // 这里需要根据您的电机驱动方式进行调整
    // 假设使用两个步进电机，通过串口控制

    // 左电机控制 (假设使用UART2)
    if(left_speed >= 0)
    {
        // 正转
        Emm_V5_Vel_Control(&huart2, 0x01, 0, left_speed, 0);
    }
    else
    {
        // 反转
        Emm_V5_Vel_Control(&huart2, 0x01, 1, -left_speed, 0);
    }

    // 右电机控制 (假设使用UART5)
    if(right_speed >= 0)
    {
        // 正转
        Emm_V5_Vel_Control(&huart5, 0x01, 0, right_speed, 0);
    }
    else
    {
        // 反转
        Emm_V5_Vel_Control(&huart5, 0x01, 1, -right_speed, 0);
    }
}

/**
 * @brief 获取传感器状态字符串
 * @param status_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_line_sensor_status(char *status_str, uint16_t max_len)
{
    if(status_str == NULL || max_len == 0)
        return;

    char temp_str[256];
    snprintf(temp_str, sizeof(temp_str),
             "Line Sensor Status:\r\n"
             "Enabled: %s\r\n"
             "State: %d (%s)\r\n"
             "Line Position: %.2f\r\n"
             "Sensors on Line: %d\r\n"
             "Threshold: %d\r\n"
             "Raw Values: ",
             line_config.enabled ? "YES" : "NO",
             line_sensor.state,
             get_state_name(line_sensor.state),
             line_sensor.line_position,
             line_sensor.sensors_on_line,
             line_sensor.threshold);

    // 添加原始传感器值
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        char value_str[16];
        snprintf(value_str, sizeof(value_str), "%d ", line_sensor.raw_values[i]);
        strcat(temp_str, value_str);
    }
    strcat(temp_str, "\r\nDigital Values: ");

    // 添加数字化值
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        char value_str[4];
        snprintf(value_str, sizeof(value_str), "%d ", line_sensor.digital_values[i]);
        strcat(temp_str, value_str);
    }
    strcat(temp_str, "\r\n");

    // 复制到输出缓冲区
    strncpy(status_str, temp_str, max_len - 1);
    status_str[max_len - 1] = '\0';
}

/**
 * @brief 获取状态名称字符串
 * @param state 状态值
 * @return 状态名称字符串
 */
static const char* get_state_name(LineState_t state)
{
    switch(state)
    {
        case LINE_STATE_NORMAL: return "NORMAL";
        case LINE_STATE_LOST: return "LOST";
        case LINE_STATE_INTERSECTION: return "INTERSECTION";
        case LINE_STATE_LEFT_TURN: return "LEFT_TURN";
        case LINE_STATE_RIGHT_TURN: return "RIGHT_TURN";
        case LINE_STATE_SHARP_LEFT: return "SHARP_LEFT";
        case LINE_STATE_SHARP_RIGHT: return "SHARP_RIGHT";
        case LINE_STATE_END: return "END";
        case LINE_STATE_STOP: return "STOP";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 获取循迹统计信息
 * @param stats_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_line_follow_stats(char *stats_str, uint16_t max_len)
{
    if(stats_str == NULL || max_len == 0)
        return;

    uint32_t runtime_ms = HAL_GetTick();
    uint32_t runtime_sec = runtime_ms / 1000;

    snprintf(stats_str, max_len,
             "Line Following Statistics:\r\n"
             "Runtime: %lu seconds\r\n"
             "Total Cycles: %lu\r\n"
             "Line Lost Count: %lu\r\n"
             "Turn Count: %lu\r\n"
             "Intersection Count: %lu\r\n"
             "Motor Speeds: L=%d, R=%d\r\n"
             "PID Output: %.2f\r\n"
             "PID Error: %.2f\r\n",
             runtime_sec,
             line_stats.total_cycles,
             line_stats.line_lost_count,
             line_stats.turn_count,
             line_stats.intersection_count,
             motor_control.left_speed,
             motor_control.right_speed,
             line_pid.output,
             line_pid.error);
}

/**
 * @brief 设置循迹参数
 * @param kp PID比例系数
 * @param ki PID积分系数
 * @param kd PID微分系数
 * @param base_speed 基础速度
 */
void set_line_follow_params(float kp, float ki, float kd, uint16_t base_speed)
{
    line_pid.kp = kp;
    line_pid.ki = ki;
    line_pid.kd = kd;
    motor_control.base_speed = base_speed;

    // 重置PID状态
    line_pid.error = 0;
    line_pid.last_error = 0;
    line_pid.integral = 0;
    line_pid.derivative = 0;
    line_pid.output = 0;

    uart_print("Line follow params updated: KP=%.2f, KI=%.2f, KD=%.2f, Speed=%d\r\n",
              kp, ki, kd, base_speed);
}

/**
 * @brief 循迹任务函数 (定时器回调)
 */
void line_following_task(void)
{
    // 检查ADC转换是否完成
    if(is_adc_conversion_complete())
    {
        // 执行循迹控制
        line_following_control();
    }
}
