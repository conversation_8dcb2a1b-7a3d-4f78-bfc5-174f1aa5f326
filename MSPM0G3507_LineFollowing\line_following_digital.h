/**
 * @file line_following_digital.h
 * @brief MSPM0G3507数字式八路灰度传感器循迹控制系统头文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef LINE_FOLLOWING_DIGITAL_H
#define LINE_FOLLOWING_DIGITAL_H

#include "ti_msp_dl_config.h"
#include "digital_line_sensor.h"
#include <stdint.h>
#include <stdbool.h>

/* 循迹系统参数定义 */
#define LINE_WIDTH_CM 2               // 黑线宽度 (cm)
#define SENSOR_SPACING_MM 10          // 传感器间距 (mm)

/* 电机控制参数 */
#define MOTOR_BASE_SPEED 50           // 基础速度 (PWM占空比 0-100)
#define MOTOR_MAX_SPEED 80            // 最大速度
#define MOTOR_MIN_SPEED 20            // 最小速度
#define MOTOR_TURN_SPEED_RATIO 0.3f   // 转弯时内侧电机速度比例

/* PID控制参数 */
#define LINE_PID_KP 20.0f             // 比例系数 (数字传感器需要更大的Kp)
#define LINE_PID_KI 0.8f              // 积分系数
#define LINE_PID_KD 10.0f             // 微分系数
#define LINE_PID_MAX_OUTPUT 40        // PID最大输出

/* 循迹状态定义 */
typedef enum {
    LINE_STATE_NORMAL = 0,            // 正常循迹
    LINE_STATE_LOST,                  // 丢失黑线
    LINE_STATE_INTERSECTION,          // 十字路口
    LINE_STATE_LEFT_TURN,             // 左转弯
    LINE_STATE_RIGHT_TURN,            // 右转弯
    LINE_STATE_SHARP_LEFT,            // 急左转
    LINE_STATE_SHARP_RIGHT,           // 急右转
    LINE_STATE_END,                   // 终点
    LINE_STATE_STOP,                  // 停止
    LINE_STATE_ERROR                  // 传感器错误
} LineState_t;

/* PID控制器结构 */
typedef struct {
    float kp, ki, kd;                 // PID参数
    float error;                      // 当前误差
    float last_error;                 // 上次误差
    float integral;                   // 积分值
    float derivative;                 // 微分值
    float output;                     // 输出值
    float max_output;                 // 最大输出限制
} LinePID_t;

/* 电机控制结构 */
typedef struct {
    int16_t left_speed;               // 左电机速度 (-100 到 +100)
    int16_t right_speed;              // 右电机速度 (-100 到 +100)
    uint16_t base_speed;              // 基础速度
    uint8_t direction;                // 运动方向 (0-前进, 1-后退)
} MotorControl_t;

/* 循迹系统配置结构 */
typedef struct {
    bool enabled;                     // 循迹使能
    bool debug_mode;                  // 调试模式
    uint16_t lost_timeout_ms;         // 丢线超时时间
    uint16_t turn_delay_ms;           // 转弯延时
    float speed_factor;               // 速度因子
    uint16_t sensor_read_interval_ms; // 传感器读取间隔
} LineFollowConfig_t;

/* 循迹统计信息结构 */
typedef struct {
    uint32_t total_cycles;            // 总循环次数
    uint32_t line_lost_count;         // 丢线次数
    uint32_t turn_count;              // 转弯次数
    uint32_t intersection_count;      // 路口次数
    uint32_t error_count;             // 错误次数
    uint32_t last_update_time;        // 最后更新时间
} LineFollowStats_t;

/* 外部变量声明 */
extern LinePID_t line_pid;
extern MotorControl_t motor_control;
extern LineFollowConfig_t line_config;
extern LineFollowStats_t line_stats;
extern LineState_t current_line_state;

/* 函数声明 */

/**
 * @brief 初始化数字式循迹系统
 */
void digital_line_following_init(void);

/**
 * @brief 检测循迹状态
 * @return 当前状态
 */
LineState_t detect_digital_line_state(void);

/**
 * @brief PID控制计算
 * @param setpoint 设定值 (通常为0)
 * @param measured_value 测量值 (黑线位置)
 * @return PID输出
 */
float digital_line_pid_calculate(float setpoint, float measured_value);

/**
 * @brief 电机控制
 * @param pid_output PID输出值
 */
void digital_control_motors(float pid_output);

/**
 * @brief 处理特殊状态 (转弯、路口等)
 */
void handle_digital_special_states(void);

/**
 * @brief 数字式循迹主控制函数
 */
void digital_line_following_control(void);

/**
 * @brief 停止电机
 */
void stop_motors(void);

/**
 * @brief 设置电机速度
 * @param left_speed 左电机速度 (-100到+100)
 * @param right_speed 右电机速度 (-100到+100)
 */
void set_motor_speeds(int16_t left_speed, int16_t right_speed);

/**
 * @brief 获取循迹统计信息
 * @param stats_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_digital_line_follow_stats(char *stats_str, uint16_t max_len);

/**
 * @brief 设置循迹参数
 * @param kp PID比例系数
 * @param ki PID积分系数
 * @param kd PID微分系数
 * @param base_speed 基础速度
 */
void set_digital_line_follow_params(float kp, float ki, float kd, uint16_t base_speed);

/**
 * @brief 数字式循迹任务函数
 */
void digital_line_following_task(void);

/**
 * @brief 获取系统时间(毫秒)
 */
uint32_t get_system_time_ms(void);

/**
 * @brief 获取状态名称字符串
 * @param state 状态值
 * @return 状态名称字符串
 */
const char* get_digital_state_name(LineState_t state);

#endif /* LINE_FOLLOWING_DIGITAL_H */
