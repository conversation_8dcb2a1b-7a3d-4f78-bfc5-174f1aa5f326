/**
 * @file adc_config.h
 * @brief MSPM0G3507 ADC配置头文件 - 八路灰度传感器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ADC_CONFIG_H
#define ADC_CONFIG_H

#include "ti_msp_dl_config.h"
#include <stdint.h>

/* 灰度传感器数量定义 */
#define GRAY_SENSOR_COUNT 8

/* ADC通道定义 */
#define ADC_CH0_SENSOR0  0  // PA27 - 最左侧传感器
#define ADC_CH1_SENSOR1  1  // PA26 - 左侧传感器
#define ADC_CH2_SENSOR2  2  // PA25 - 左中传感器
#define ADC_CH3_SENSOR3  3  // PA24 - 中心左传感器
#define ADC_CH4_SENSOR4  4  // PA17 - 中心右传感器
#define ADC_CH5_SENSOR5  5  // PA16 - 右中传感器
#define ADC_CH6_SENSOR6  6  // PA15 - 右侧传感器
#define ADC_CH7_SENSOR7  7  // PA14 - 最右侧传感器

/* ADC参数定义 */
#define ADC_RESOLUTION_12BIT    4095
#define ADC_VREF_VOLTAGE        3.3f
#define ADC_THRESHOLD_DEFAULT   2000

/* 外部变量声明 */
extern uint16_t adc_values[GRAY_SENSOR_COUNT];
extern uint8_t adc_conversion_complete;

/* 函数声明 */

/**
 * @brief 初始化ADC配置
 */
void adc_init(void);

/**
 * @brief 读取单个ADC通道值
 * @param channel ADC通道号 (0-7)
 * @return ADC值 (0-4095)
 */
uint16_t adc_read_channel(uint8_t channel);

/**
 * @brief 读取所有ADC通道值
 * @param values 输出数组指针
 */
void adc_read_all_channels(uint16_t *values);

/**
 * @brief 启动ADC转换
 */
void adc_start_conversion(void);

/**
 * @brief 检查ADC转换是否完成
 * @return 1-完成 0-未完成
 */
uint8_t adc_is_conversion_complete(void);

/**
 * @brief 将ADC值转换为电压
 * @param adc_value ADC原始值
 * @return 电压值(V)
 */
float adc_to_voltage(uint16_t adc_value);

#endif /* ADC_CONFIG_H */
