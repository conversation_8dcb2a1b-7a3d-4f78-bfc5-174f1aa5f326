/**
 * @file analog_line_sensor.h
 * @brief MSPM0G3507模拟输出八路灰度传感器头文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef ANALOG_LINE_SENSOR_H
#define ANALOG_LINE_SENSOR_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>

/* 传感器引脚定义 */
#define SENSOR_OUT_PORT     GPIOA
#define SENSOR_OUT_PIN      DL_GPIO_PIN_1    // PA1 (A01) - 模拟输入

#define SENSOR_EN_PORT      GPIOA
#define SENSOR_EN_PIN       DL_GPIO_PIN_26   // PA26 - 使能控制

#define SENSOR_AD2_PORT     GPIOB
#define SENSOR_AD2_PIN      DL_GPIO_PIN_21   // PB21 (B21) - 地址位2

#define SENSOR_AD1_PORT     GPIOA
#define SENSOR_AD1_PIN      DL_GPIO_PIN_9    // PA9 (A09) - 地址位1

#define SENSOR_AD0_PORT     GPIOB
#define SENSOR_AD0_PIN      DL_GPIO_PIN_6    // PB6 (B06) - 地址位0

#define SENSOR_ERR_PORT     GPIOA
#define SENSOR_ERR_PIN      DL_GPIO_PIN_16   // PA16 - 错误检测

/* 传感器参数定义 */
#define LINE_SENSOR_COUNT 8           // 传感器数量
#define SENSOR_READ_DELAY_US 10       // 传感器读取延时(微秒)
#define SENSOR_ENABLE_DELAY_US 5      // 使能延时(微秒)
#define ADC_RESOLUTION 4095           // 12位ADC分辨率
#define DEFAULT_THRESHOLD 2000        // 默认阈值

/* 传感器数据结构 */
typedef struct {
    uint16_t raw_values[LINE_SENSOR_COUNT];     // 原始ADC值
    uint8_t digital_values[LINE_SENSOR_COUNT];  // 数字化值 (0/1)
    uint16_t threshold;                         // 检测阈值
    float line_position;                        // 黑线位置 (-3.5 到 +3.5)
    uint8_t sensors_on_line;                    // 检测到黑线的传感器数量
    bool error_flag;                            // 错误标志
    uint32_t last_read_time;                    // 最后读取时间
    bool calibrated;                            // 是否已校准
    uint16_t min_values[LINE_SENSOR_COUNT];     // 校准最小值
    uint16_t max_values[LINE_SENSOR_COUNT];     // 校准最大值
} AnalogLineSensor_t;

/* 外部变量声明 */
extern AnalogLineSensor_t analog_line_sensor;

/* 函数声明 */

/**
 * @brief 初始化模拟灰度传感器
 */
void analog_line_sensor_init(void);

/**
 * @brief 设置传感器地址
 * @param address 传感器地址 (0-7)
 */
void set_analog_sensor_address(uint8_t address);

/**
 * @brief 使能传感器
 * @param enable true-使能, false-禁用
 */
void enable_analog_sensor(bool enable);

/**
 * @brief 读取单个传感器ADC值
 * @param sensor_index 传感器索引 (0-7)
 * @return ADC值 (0-4095)
 */
uint16_t read_single_analog_sensor(uint8_t sensor_index);

/**
 * @brief 读取所有传感器值
 */
void read_all_analog_sensors(void);

/**
 * @brief 校准传感器
 * @param calibration_time_ms 校准时间(毫秒)
 */
void calibrate_analog_sensors(uint16_t calibration_time_ms);

/**
 * @brief 计算黑线位置
 * @return 黑线位置 (-3.5 到 +3.5)
 */
float calculate_analog_line_position(void);

/**
 * @brief 检查传感器错误状态
 * @return true-有错误, false-正常
 */
bool check_analog_sensor_error(void);

/**
 * @brief 获取传感器状态字符串
 * @param status_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_analog_sensor_status(char *status_str, uint16_t max_len);

/**
 * @brief 设置检测阈值
 * @param threshold 新阈值 (0-4095)
 */
void set_analog_threshold(uint16_t threshold);

/**
 * @brief 微秒延时函数
 * @param us 延时时间(微秒)
 */
void delay_us(uint32_t us);

/**
 * @brief 获取系统时间(毫秒)
 */
uint32_t get_system_time_ms(void);

#endif /* ANALOG_LINE_SENSOR_H */
