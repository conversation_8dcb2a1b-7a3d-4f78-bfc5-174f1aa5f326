# MSPM0G3507数字式灰度传感器SysConfig配置指南

## 概述

本文档说明如何在CCS的SysConfig工具中配置MSPM0G3507的GPIO和外设，以支持数字式八路灰度传感器循迹系统。

## 🔌 GPIO配置

### 1. 数字式传感器GPIO配置

在SysConfig中配置以下GPIO引脚：

| 功能 | 引脚 | 方向 | 初始状态 | 说明 |
|------|------|------|----------|------|
| OUT | PA1 | Input | Pull-up | 传感器数字输出 |
| EN | PA26 | Output | Low | 传感器使能控制 |
| AD2 | PB21 | Output | Low | 地址位2 |
| AD1 | PA9 | Output | Low | 地址位1 |
| AD0 | PB6 | Output | Low | 地址位0 |
| ERR | PA16 | Input | Pull-up | 错误检测输入 |

### 2. GPIO配置步骤

1. **添加GPIO外设**
   - 在SysConfig中点击左侧的 **GPIO**
   - 点击 **ADD** 添加GPIO配置

2. **配置输入引脚**
   ```
   PA1 (OUT):
   - Direction: Input
   - Internal Resistor: Pull-up
   - Interrupt: Disabled

   PA16 (ERR):
   - Direction: Input
   - Internal Resistor: Pull-up
   - Interrupt: Disabled
   ```

3. **配置输出引脚**
   ```
   PA26 (EN):
   - Direction: Output
   - Initial Value: Low
   - Drive Strength: Standard

   PB21 (AD2):
   - Direction: Output
   - Initial Value: Low
   - Drive Strength: Standard

   PA9 (AD1):
   - Direction: Output
   - Initial Value: Low
   - Drive Strength: Standard

   PB6 (AD0):
   - Direction: Output
   - Initial Value: Low
   - Drive Strength: Standard
   ```

## ⚡ PWM配置

### 1. 电机PWM配置

配置两个PWM实例用于电机控制：

**PWM_0 (左电机):**
```
Timer Instance: TIMG0
Clock Source: BUSCLK (32MHz)
Clock Prescaler: 32 (1MHz timer clock)
Timer Period: 1000 (1kHz PWM frequency)
PWM Mode: Edge Aligned
CC0 Pin: PB20 (左电机PWM1)
CC1 Pin: PB13 (左电机PWM2)
Initial Duty Cycle: 0%
```

**PWM_1 (右电机):**
```
Timer Instance: TIMG1
Clock Source: BUSCLK (32MHz)
Clock Prescaler: 32 (1MHz timer clock)
Timer Period: 1000 (1kHz PWM frequency)
PWM Mode: Edge Aligned
CC0 Pin: PB4 (右电机PWM1)
CC1 Pin: PB1 (右电机PWM2)
Initial Duty Cycle: 0%
```

### 2. 电机方向控制GPIO

| 功能 | 引脚 | 方向 | 初始状态 |
|------|------|------|----------|
| 左电机DIR1 | PB19 | Output | Low |
| 左电机DIR2 | PB12 | Output | Low |
| 右电机DIR1 | PB3 | Output | Low |
| 右电机DIR2 | PB0 | Output | Low |

## 📡 UART配置

### 1. UART基本配置
```
Instance: UART0
Baud Rate: 115200
Data Bits: 8
Parity: None
Stop Bits: 1
Flow Control: None
TX Pin: PA10
RX Pin: PA11
```

### 2. UART中断配置
```
Enable RX Interrupt: Yes
Interrupt Priority: 1
FIFO Threshold: 1/8 full
```

## ⏰ Timer配置

### 1. 系统定时器配置

配置一个定时器用于系统时间计数：

```
Timer Instance: TIMG2
Timer Mode: Periodic
Clock Source: BUSCLK (32MHz)
Clock Prescaler: 32000 (1kHz timer clock)
Timer Period: 1 (1ms period)
Enable Interrupt: Yes
Interrupt Priority: 2
```

## 🔧 系统配置

### 1. 时钟配置
```
SYSOSC: 32MHz (内部振荡器)
MCLK: 32MHz
BUSCLK: 32MHz
```

### 2. 电源配置
```
VDD: 3.3V
VREF: VDD
```

## 📝 数字传感器工作原理

### 1. 地址选择机制

数字式传感器通过3位地址线选择要读取的传感器：

| AD2 | AD1 | AD0 | 传感器 | 位置 |
|-----|-----|-----|--------|------|
| 0 | 0 | 0 | 传感器0 | 最左侧 |
| 0 | 0 | 1 | 传感器1 | 左侧 |
| 0 | 1 | 0 | 传感器2 | 左中 |
| 0 | 1 | 1 | 传感器3 | 中心左 |
| 1 | 0 | 0 | 传感器4 | 中心右 |
| 1 | 0 | 1 | 传感器5 | 右中 |
| 1 | 1 | 0 | 传感器6 | 右侧 |
| 1 | 1 | 1 | 传感器7 | 最右侧 |

### 2. 读取时序

```
1. 设置地址线 (AD2, AD1, AD0)
2. 使能传感器 (EN = High)
3. 等待稳定时间 (5-10μs)
4. 读取OUT引脚状态
5. 禁用传感器 (EN = Low)
```

### 3. 信号定义

```
OUT引脚:
- Low (0V): 检测到黑线
- High (3.3V/5V): 检测到白色区域

ERR引脚:
- Low (0V): 传感器错误
- High (3.3V/5V): 传感器正常
```

## ⚠️ 注意事项

### 1. 电源要求

- **传感器供电**: 5V (如果开发板只有3.3V输出，需要外部5V电源)
- **信号电平**: 检查传感器输出是否与MCU兼容
- **电流消耗**: 确保电源能提供足够电流

### 2. 时序要求

- **地址设置延时**: 设置地址后需要5μs延时
- **使能延时**: 使能后需要10μs延时
- **读取间隔**: 建议20ms读取间隔

### 3. 引脚冲突检查

- 确保GPIO引脚没有被其他外设占用
- 检查PWM引脚分配是否正确
- 验证UART引脚配置

## 🔍 调试建议

### 1. 硬件测试

```c
// 测试单个传感器读取
for(uint8_t i = 0; i < 8; i++) {
    uint8_t value = read_single_sensor(i);
    printf("Sensor %d: %d\n", i, value);
}
```

### 2. 地址线测试

```c
// 测试地址设置
for(uint8_t addr = 0; addr < 8; addr++) {
    set_sensor_address(addr);
    delay_us(10);
    // 检查地址线状态
}
```

### 3. 错误检测测试

```c
// 检查错误状态
bool error = check_sensor_error();
printf("Sensor error: %s\n", error ? "ERROR" : "OK");
```

## 📋 配置检查清单

- [ ] GPIO引脚配置正确
- [ ] PWM频率和占空比设置合适
- [ ] UART波特率匹配
- [ ] 定时器中断优先级设置
- [ ] 电源电压匹配
- [ ] 引脚无冲突
- [ ] 时序参数合理

---
*配置完成后，系统即可支持数字式八路灰度传感器的循迹功能*
