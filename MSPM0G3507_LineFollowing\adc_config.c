/**
 * @file adc_config.c
 * @brief MSPM0G3507 ADC配置实现文件 - 八路灰度传感器
 * <AUTHOR>
 * @date 2024
 */

#include "adc_config.h"
#include "ti/driverlib/dl_adc12.h"

/* 全局变量定义 */
uint16_t adc_values[GRAY_SENSOR_COUNT];
uint8_t adc_conversion_complete = 0;

/**
 * @brief 初始化ADC配置
 */
void adc_init(void)
{
    // ADC初始化在ti_msp_dl_config.c中通过SysConfig完成
    // 这里只需要清零缓冲区
    for(uint8_t i = 0; i < GRAY_SENSOR_COUNT; i++)
    {
        adc_values[i] = 0;
    }
    adc_conversion_complete = 0;
}

/**
 * @brief 读取单个ADC通道值
 * @param channel ADC通道号 (0-7)
 * @return ADC值 (0-4095)
 */
uint16_t adc_read_channel(uint8_t channel)
{
    uint16_t adc_result = 0;
    
    if(channel >= GRAY_SENSOR_COUNT)
        return 0;
    
    // 使能ADC转换
    DL_ADC12_enableConversions(ADC_VOLTAGE_INST);
    
    // 根据通道选择对应的内存结果寄存器
    switch(channel)
    {
        case 0:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);
            break;
        case 1:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH1);
            break;
        case 2:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH2);
            break;
        case 3:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH3);
            break;
        case 4:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH4);
            break;
        case 5:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH5);
            break;
        case 6:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH6);
            break;
        case 7:
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while(DL_ADC12_getStatus(ADC_VOLTAGE_INST) != DL_ADC12_STATUS_CONVERSION_IDLE);
            adc_result = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH7);
            break;
        default:
            break;
    }
    
    // 停止转换并失能ADC
    DL_ADC12_stopConversion(ADC_VOLTAGE_INST);
    DL_ADC12_disableConversions(ADC_VOLTAGE_INST);
    
    return adc_result;
}

/**
 * @brief 读取所有ADC通道值
 * @param values 输出数组指针
 */
void adc_read_all_channels(uint16_t *values)
{
    for(uint8_t i = 0; i < GRAY_SENSOR_COUNT; i++)
    {
        values[i] = adc_read_channel(i);
        // 短暂延时，避免通道切换过快
        delay_cycles(1000);
    }
}

/**
 * @brief 启动ADC转换
 */
void adc_start_conversion(void)
{
    DL_ADC12_enableConversions(ADC_VOLTAGE_INST);
    DL_ADC12_startConversion(ADC_VOLTAGE_INST);
}

/**
 * @brief 检查ADC转换是否完成
 * @return 1-完成 0-未完成
 */
uint8_t adc_is_conversion_complete(void)
{
    return (DL_ADC12_getStatus(ADC_VOLTAGE_INST) == DL_ADC12_STATUS_CONVERSION_IDLE);
}

/**
 * @brief 将ADC值转换为电压
 * @param adc_value ADC原始值
 * @return 电压值(V)
 */
float adc_to_voltage(uint16_t adc_value)
{
    return (float)adc_value / ADC_RESOLUTION_12BIT * ADC_VREF_VOLTAGE;
}
