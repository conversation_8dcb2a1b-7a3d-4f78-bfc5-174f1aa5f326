/**
 * @file main_digital.c
 * @brief MSPM0G3507数字式八路灰度传感器循迹主程序
 * <AUTHOR>
 * @date 2024
 */

#include "ti_msp_dl_config.h"
#include "line_following_digital.h"
#include "digital_line_sensor.h"
#include <stdio.h>
#include <string.h>

/* 串口发送函数 */
void uart_send_string(char* str);
void uart_send_char(char ch);

/* 命令处理函数 */
void process_uart_commands(void);

/* 全局变量 */
static char uart_rx_buffer[64];
static uint8_t uart_rx_index = 0;
static bool uart_cmd_ready = false;

int main(void)
{
    char output_buffer[256];
    uint32_t last_debug_time = 0;
    uint32_t last_sensor_read_time = 0;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化数字式循迹系统
    digital_line_following_init();
    
    uart_send_string("MSPM0G3507 Digital Line Following System Started\r\n");
    uart_send_string("Sensor Type: Digital 8-Channel Gray Scale Sensor\r\n");
    uart_send_string("Commands:\r\n");
    uart_send_string("  L1 - Start line following\r\n");
    uart_send_string("  L0 - Stop line following\r\n");
    uart_send_string("  LS - Show sensor status\r\n");
    uart_send_string("  LT - Show statistics\r\n");
    uart_send_string("  LD - Toggle debug mode\r\n");
    uart_send_string("  LR - Reset statistics\r\n");
    uart_send_string("  LE - Test sensor error\r\n");
    uart_send_string("Ready!\r\n\r\n");
    
    while (1)
    {
        uint32_t current_time = get_system_time_ms();
        
        // 处理串口命令
        process_uart_commands();
        
        // 定时读取传感器数据
        if((current_time - last_sensor_read_time) >= line_config.sensor_read_interval_ms)
        {
            read_all_sensors();
            calculate_digital_line_position();
            last_sensor_read_time = current_time;
        }
        
        // 执行循迹控制
        digital_line_following_task();
        
        // 调试信息输出 (每500ms)
        if(line_config.debug_mode && 
           (current_time - last_debug_time) > 500)
        {
            sprintf(output_buffer, 
                   "Sensors: %d%d%d%d%d%d%d%d | Pos: %.2f | State: %s | L: %d R: %d | Err: %s\r\n",
                   digital_line_sensor.digital_values[0], digital_line_sensor.digital_values[1],
                   digital_line_sensor.digital_values[2], digital_line_sensor.digital_values[3],
                   digital_line_sensor.digital_values[4], digital_line_sensor.digital_values[5],
                   digital_line_sensor.digital_values[6], digital_line_sensor.digital_values[7],
                   digital_line_sensor.line_position, 
                   get_digital_state_name(current_line_state),
                   motor_control.left_speed, motor_control.right_speed,
                   digital_line_sensor.error_flag ? "ERR" : "OK");
            uart_send_string(output_buffer);
            
            last_debug_time = current_time;
        }
        
        // 短暂延时
        delay_cycles(16000); // 约0.5ms
    }
}

/**
 * @brief 串口发送字符串
 */
void uart_send_string(char* str)
{
    while(*str != 0 && str != 0)
    {
        while(DL_UART_isBusy(UART_0_INST) == true);
        DL_UART_Main_transmitData(UART_0_INST, *str++);
    }
}

/**
 * @brief 串口发送字符
 */
void uart_send_char(char ch)
{
    while(DL_UART_isBusy(UART_0_INST) == true);
    DL_UART_Main_transmitData(UART_0_INST, ch);
}

/**
 * @brief 处理串口命令
 */
void process_uart_commands(void)
{
    if(!uart_cmd_ready)
        return;
    
    char output_buffer[512];
    
    if(strncmp(uart_rx_buffer, "L1", 2) == 0)
    {
        // 启动循迹
        line_config.enabled = true;
        uart_send_string("Digital line following started\r\n");
    }
    else if(strncmp(uart_rx_buffer, "L0", 2) == 0)
    {
        // 停止循迹
        line_config.enabled = false;
        stop_motors();
        uart_send_string("Digital line following stopped\r\n");
    }
    else if(strncmp(uart_rx_buffer, "LS", 2) == 0)
    {
        // 显示传感器状态
        get_digital_sensor_status(output_buffer, sizeof(output_buffer));
        uart_send_string(output_buffer);
    }
    else if(strncmp(uart_rx_buffer, "LT", 2) == 0)
    {
        // 显示统计信息
        get_digital_line_follow_stats(output_buffer, sizeof(output_buffer));
        uart_send_string(output_buffer);
    }
    else if(strncmp(uart_rx_buffer, "LD", 2) == 0)
    {
        // 切换调试模式
        line_config.debug_mode = !line_config.debug_mode;
        sprintf(output_buffer, "Debug mode: %s\r\n", line_config.debug_mode ? "ON" : "OFF");
        uart_send_string(output_buffer);
    }
    else if(strncmp(uart_rx_buffer, "LR", 2) == 0)
    {
        // 重置统计信息
        memset(&line_stats, 0, sizeof(LineFollowStats_t));
        uart_send_string("Statistics reset\r\n");
    }
    else if(strncmp(uart_rx_buffer, "LE", 2) == 0)
    {
        // 测试传感器错误状态
        bool error_status = check_sensor_error();
        sprintf(output_buffer, "Sensor error status: %s\r\n", error_status ? "ERROR" : "OK");
        uart_send_string(output_buffer);
    }
    else if(strncmp(uart_rx_buffer, "LP", 2) == 0)
    {
        // 设置PID参数 (简化版本)
        // 格式: LP[kp,ki,kd,speed] 例如: LP20,0.8,10,50
        if(strlen(uart_rx_buffer) > 2)
        {
            float kp = 20.0f, ki = 0.8f, kd = 10.0f;
            uint16_t speed = 50;
            
            // 简单解析
            sscanf(&uart_rx_buffer[2], "%f,%f,%f,%hu", &kp, &ki, &kd, &speed);
            set_digital_line_follow_params(kp, ki, kd, speed);
        }
    }
    else
    {
        uart_send_string("Unknown command. Available commands:\r\n");
        uart_send_string("L1/L0 - Start/Stop, LS - Status, LT - Stats, LD - Debug\r\n");
        uart_send_string("LR - Reset, LE - Error test, LP[kp,ki,kd,speed] - Set params\r\n");
    }
    
    // 清除命令缓冲区
    memset(uart_rx_buffer, 0, sizeof(uart_rx_buffer));
    uart_rx_index = 0;
    uart_cmd_ready = false;
}

/**
 * @brief UART中断回调函数
 */
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST))
    {
        case DL_UART_MAIN_IIDX_RX:
        {
            uint8_t received_char = DL_UART_Main_receiveData(UART_0_INST);
            
            if(received_char == '\r' || received_char == '\n')
            {
                if(uart_rx_index > 0)
                {
                    uart_rx_buffer[uart_rx_index] = '\0';
                    uart_cmd_ready = true;
                }
            }
            else if(uart_rx_index < (sizeof(uart_rx_buffer) - 1))
            {
                uart_rx_buffer[uart_rx_index++] = received_char;
                uart_send_char(received_char); // 回显
            }
            break;
        }
        default:
            break;
    }
}
