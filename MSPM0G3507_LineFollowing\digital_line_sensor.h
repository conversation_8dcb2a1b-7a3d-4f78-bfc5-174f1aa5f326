/**
 * @file digital_line_sensor.h
 * @brief MSPM0G3507数字式八路灰度传感器头文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef DIGITAL_LINE_SENSOR_H
#define DIGITAL_LINE_SENSOR_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>

/* 传感器引脚定义 */
#define SENSOR_OUT_PORT     GPIOA
#define SENSOR_OUT_PIN      DL_GPIO_PIN_27

#define SENSOR_EN_PORT      GPIOA
#define SENSOR_EN_PIN       DL_GPIO_PIN_26

#define SENSOR_AD2_PORT     GPIOA
#define SENSOR_AD2_PIN      DL_GPIO_PIN_25

#define SENSOR_AD1_PORT     GPIOA
#define SENSOR_AD1_PIN      DL_GPIO_PIN_24

#define SENSOR_AD0_PORT     GPIOA
#define SENSOR_AD0_PIN      DL_GPIO_PIN_17

#define SENSOR_ERR_PORT     GPIOA
#define SENSOR_ERR_PIN      DL_GPIO_PIN_16

/* 传感器参数定义 */
#define LINE_SENSOR_COUNT 8           // 传感器数量
#define SENSOR_READ_DELAY_US 10       // 传感器读取延时(微秒)
#define SENSOR_ENABLE_DELAY_US 5      // 使能延时(微秒)

/* 传感器地址定义 */
#define SENSOR_ADDR_0 0x00    // 000 - 传感器0 (最左侧)
#define SENSOR_ADDR_1 0x01    // 001 - 传感器1
#define SENSOR_ADDR_2 0x02    // 010 - 传感器2
#define SENSOR_ADDR_3 0x03    // 011 - 传感器3
#define SENSOR_ADDR_4 0x04    // 100 - 传感器4
#define SENSOR_ADDR_5 0x05    // 101 - 传感器5
#define SENSOR_ADDR_6 0x06    // 110 - 传感器6
#define SENSOR_ADDR_7 0x07    // 111 - 传感器7 (最右侧)

/* 传感器数据结构 */
typedef struct {
    uint8_t digital_values[LINE_SENSOR_COUNT];  // 数字化值 (0/1)
    float line_position;                        // 黑线位置 (-3.5 到 +3.5)
    uint8_t sensors_on_line;                    // 检测到黑线的传感器数量
    bool error_flag;                            // 错误标志
    uint32_t last_read_time;                    // 最后读取时间
} DigitalLineSensor_t;

/* 外部变量声明 */
extern DigitalLineSensor_t digital_line_sensor;

/* 函数声明 */

/**
 * @brief 初始化数字式灰度传感器
 */
void digital_line_sensor_init(void);

/**
 * @brief 设置传感器地址
 * @param address 传感器地址 (0-7)
 */
void set_sensor_address(uint8_t address);

/**
 * @brief 使能传感器
 * @param enable true-使能, false-禁用
 */
void enable_sensor(bool enable);

/**
 * @brief 读取单个传感器值
 * @param sensor_index 传感器索引 (0-7)
 * @return 传感器值 (0-黑线, 1-白色)
 */
uint8_t read_single_sensor(uint8_t sensor_index);

/**
 * @brief 读取所有传感器值
 */
void read_all_sensors(void);

/**
 * @brief 计算黑线位置
 * @return 黑线位置 (-3.5 到 +3.5)
 */
float calculate_digital_line_position(void);

/**
 * @brief 检查传感器错误状态
 * @return true-有错误, false-正常
 */
bool check_sensor_error(void);

/**
 * @brief 获取传感器状态字符串
 * @param status_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_digital_sensor_status(char *status_str, uint16_t max_len);

/**
 * @brief 微秒延时函数
 * @param us 延时时间(微秒)
 */
void delay_us(uint32_t us);

#endif /* DIGITAL_LINE_SENSOR_H */
