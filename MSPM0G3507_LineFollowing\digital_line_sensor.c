/**
 * @file digital_line_sensor.c
 * @brief MSPM0G3507数字式八路灰度传感器实现
 * <AUTHOR>
 * @date 2024
 */

#include "digital_line_sensor.h"
#include "ti/driverlib/dl_gpio.h"
#include <string.h>
#include <stdio.h>

/* 全局变量定义 */
DigitalLineSensor_t digital_line_sensor;

/**
 * @brief 初始化数字式灰度传感器
 */
void digital_line_sensor_init(void)
{
    // 初始化传感器数据结构
    memset(&digital_line_sensor, 0, sizeof(DigitalLineSensor_t));
    
    // 配置GPIO引脚
    // OUT引脚配置为输入
    DL_GPIO_initDigitalInput(SENSOR_OUT_PIN);
    DL_GPIO_setPins(SENSOR_OUT_PORT, SENSOR_OUT_PIN);
    
    // EN引脚配置为输出
    DL_GPIO_initDigitalOutput(SENSOR_EN_PIN);
    DL_GPIO_clearPins(SENSOR_EN_PORT, SENSOR_EN_PIN); // 初始禁用
    
    // AD2, AD1, AD0引脚配置为输出
    DL_GPIO_initDigitalOutput(SENSOR_AD2_PIN);
    DL_GPIO_initDigitalOutput(SENSOR_AD1_PIN);
    DL_GPIO_initDigitalOutput(SENSOR_AD0_PIN);
    
    // 清除地址引脚
    DL_GPIO_clearPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
    DL_GPIO_clearPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
    DL_GPIO_clearPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
    
    // ERR引脚配置为输入
    DL_GPIO_initDigitalInput(SENSOR_ERR_PIN);
    DL_GPIO_setPins(SENSOR_ERR_PORT, SENSOR_ERR_PIN);
    
    // 初始化完成，使能传感器
    enable_sensor(true);
}

/**
 * @brief 设置传感器地址
 * @param address 传感器地址 (0-7)
 */
void set_sensor_address(uint8_t address)
{
    if(address > 7) return;
    
    // 设置AD0位
    if(address & 0x01)
        DL_GPIO_setPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
    else
        DL_GPIO_clearPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);
    
    // 设置AD1位
    if(address & 0x02)
        DL_GPIO_setPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
    else
        DL_GPIO_clearPins(SENSOR_AD1_PORT, SENSOR_AD1_PIN);
    
    // 设置AD2位
    if(address & 0x04)
        DL_GPIO_setPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
    else
        DL_GPIO_clearPins(SENSOR_AD2_PORT, SENSOR_AD2_PIN);
    
    // 地址设置后需要短暂延时
    delay_us(SENSOR_ENABLE_DELAY_US);
}

/**
 * @brief 使能传感器
 * @param enable true-使能, false-禁用
 */
void enable_sensor(bool enable)
{
    if(enable)
    {
        DL_GPIO_setPins(SENSOR_EN_PORT, SENSOR_EN_PIN);
    }
    else
    {
        DL_GPIO_clearPins(SENSOR_EN_PORT, SENSOR_EN_PIN);
    }
    
    // 使能状态改变后需要延时
    delay_us(SENSOR_ENABLE_DELAY_US);
}

/**
 * @brief 读取单个传感器值
 * @param sensor_index 传感器索引 (0-7)
 * @return 传感器值 (0-检测到黑线, 1-白色区域)
 */
uint8_t read_single_sensor(uint8_t sensor_index)
{
    if(sensor_index >= LINE_SENSOR_COUNT)
        return 1; // 默认返回白色
    
    // 设置传感器地址
    set_sensor_address(sensor_index);
    
    // 延时等待传感器稳定
    delay_us(SENSOR_READ_DELAY_US);
    
    // 读取OUT引脚状态
    uint32_t pin_state = DL_GPIO_readPins(SENSOR_OUT_PORT, SENSOR_OUT_PIN);
    
    // 返回传感器值 (假设低电平表示检测到黑线)
    return (pin_state == 0) ? 1 : 0;  // 1表示检测到黑线，0表示白色
}

/**
 * @brief 读取所有传感器值
 */
void read_all_sensors(void)
{
    // 检查传感器错误状态
    digital_line_sensor.error_flag = check_sensor_error();
    
    if(digital_line_sensor.error_flag)
    {
        // 如果有错误，清零所有传感器值
        memset(digital_line_sensor.digital_values, 0, LINE_SENSOR_COUNT);
        digital_line_sensor.sensors_on_line = 0;
        return;
    }
    
    // 读取所有8个传感器
    digital_line_sensor.sensors_on_line = 0;
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        digital_line_sensor.digital_values[i] = read_single_sensor(i);
        if(digital_line_sensor.digital_values[i])
        {
            digital_line_sensor.sensors_on_line++;
        }
    }
    
    // 更新读取时间
    digital_line_sensor.last_read_time = get_system_time_ms();
}

/**
 * @brief 计算黑线位置
 * @return 黑线位置 (-3.5 到 +3.5)
 */
float calculate_digital_line_position(void)
{
    uint32_t weighted_sum = 0;
    uint32_t total_weight = 0;
    
    // 使用数字值进行加权平均计算
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        if(digital_line_sensor.digital_values[i])
        {
            // 传感器位置: 0->-3.5, 1->-2.5, ..., 7->+3.5
            int16_t position = (i * 1000) - 3500;  // 乘以1000避免浮点运算
            weighted_sum += position;
            total_weight += 1;  // 数字传感器权重都为1
        }
    }
    
    if(total_weight > 0)
    {
        digital_line_sensor.line_position = (float)weighted_sum / (float)total_weight / 1000.0f;
    }
    // 如果没有检测到黑线，保持上次位置不变
    
    return digital_line_sensor.line_position;
}

/**
 * @brief 检查传感器错误状态
 * @return true-有错误, false-正常
 */
bool check_sensor_error(void)
{
    // 读取ERR引脚状态
    uint32_t err_state = DL_GPIO_readPins(SENSOR_ERR_PORT, SENSOR_ERR_PIN);
    
    // 假设低电平表示有错误
    return (err_state == 0);
}

/**
 * @brief 获取传感器状态字符串
 * @param status_str 输出字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_digital_sensor_status(char *status_str, uint16_t max_len)
{
    if(status_str == NULL || max_len == 0)
        return;
    
    char temp_str[256];
    snprintf(temp_str, sizeof(temp_str),
             "Digital Line Sensor Status:\r\n"
             "Digital Values: ");
    
    // 添加数字化值
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        char value_str[4];
        snprintf(value_str, sizeof(value_str), "%d", digital_line_sensor.digital_values[i]);
        strcat(temp_str, value_str);
    }
    
    char info_str[128];
    snprintf(info_str, sizeof(info_str),
             "\r\nLine Position: %.2f\r\n"
             "Sensors on Line: %d\r\n"
             "Error Flag: %s\r\n"
             "Last Read Time: %lu ms\r\n",
             digital_line_sensor.line_position,
             digital_line_sensor.sensors_on_line,
             digital_line_sensor.error_flag ? "ERROR" : "OK",
             digital_line_sensor.last_read_time);
    
    strcat(temp_str, info_str);
    
    // 复制到输出缓冲区
    strncpy(status_str, temp_str, max_len - 1);
    status_str[max_len - 1] = '\0';
}

/**
 * @brief 微秒延时函数
 * @param us 延时时间(微秒)
 */
void delay_us(uint32_t us)
{
    // 假设系统时钟32MHz，每个时钟周期约31.25ns
    // 1微秒需要约32个时钟周期
    uint32_t cycles = us * 32;
    
    // 简单的循环延时
    for(uint32_t i = 0; i < cycles; i++)
    {
        __asm("nop");
    }
}
