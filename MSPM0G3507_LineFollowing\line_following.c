/**
 * @file line_following.c
 * @brief MSPM0G3507八路灰度传感器循迹控制系统实现
 * <AUTHOR>
 * @date 2024
 */

#include "line_following.h"
#include "ti/driverlib/dl_timer.h"
#include "ti/driverlib/dl_gpio.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

/* 全局变量定义 */
LineSensor_t line_sensor;
LinePID_t line_pid;
MotorControl_t motor_control;
LineFollowConfig_t line_config;

/* 校准数据 */
static uint16_t sensor_min[LINE_SENSOR_COUNT];
static uint16_t sensor_max[LINE_SENSOR_COUNT];
static bool calibration_done = false;

/* 系统时间计数器 */
static volatile uint32_t system_time_ms = 0;

/**
 * @brief 初始化循迹系统
 */
void line_following_init(void)
{
    // 初始化ADC
    adc_init();
    
    // 初始化传感器数据
    memset(&line_sensor, 0, sizeof(LineSensor_t));
    line_sensor.threshold = LINE_THRESHOLD_DEFAULT;
    line_sensor.state = LINE_STATE_STOP;
    
    // 初始化PID控制器
    line_pid.kp = LINE_PID_KP;
    line_pid.ki = LINE_PID_KI;
    line_pid.kd = LINE_PID_KD;
    line_pid.max_output = LINE_PID_MAX_OUTPUT;
    line_pid.error = 0;
    line_pid.last_error = 0;
    line_pid.integral = 0;
    line_pid.derivative = 0;
    line_pid.output = 0;
    
    // 初始化电机控制
    motor_control.base_speed = MOTOR_BASE_SPEED;
    motor_control.left_speed = 0;
    motor_control.right_speed = 0;
    motor_control.direction = 0;
    
    // 初始化配置
    line_config.enabled = false;
    line_config.debug_mode = false;
    line_config.lost_timeout_ms = 1000;
    line_config.turn_delay_ms = 200;
    line_config.auto_calibration = true;
    line_config.speed_factor = 1.0f;
    
    // 初始化校准数据
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        sensor_min[i] = 4095;
        sensor_max[i] = 0;
    }
    
    // 停止电机
    stop_motors();
}

/**
 * @brief 读取传感器数据
 */
void read_line_sensors(void)
{
    // 获取ADC原始值
    adc_read_all_channels(line_sensor.raw_values);
    
    // 数字化处理
    line_sensor.sensors_on_line = 0;
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        if(line_sensor.raw_values[i] > line_sensor.threshold)
        {
            line_sensor.digital_values[i] = 1;  // 检测到黑线
            line_sensor.sensors_on_line++;
        }
        else
        {
            line_sensor.digital_values[i] = 0;  // 白色区域
        }
    }
    
    // 更新最后检测时间
    if(line_sensor.sensors_on_line > 0)
    {
        line_sensor.last_seen_time = get_system_time_ms();
    }
}

/**
 * @brief 校准传感器
 * @param calibration_time_ms 校准时间(毫秒)
 */
void calibrate_line_sensors(uint16_t calibration_time_ms)
{
    uint32_t start_time = get_system_time_ms();
    uint16_t temp_values[LINE_SENSOR_COUNT];
    
    // 重置校准数据
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        sensor_min[i] = 4095;
        sensor_max[i] = 0;
    }
    
    // 校准过程
    while((get_system_time_ms() - start_time) < calibration_time_ms)
    {
        adc_read_all_channels(temp_values);
        
        for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
        {
            if(temp_values[i] < sensor_min[i])
                sensor_min[i] = temp_values[i];
            if(temp_values[i] > sensor_max[i])
                sensor_max[i] = temp_values[i];
        }
        
        delay_cycles(32000); // 约1ms延时
    }
    
    // 计算阈值 (最大值和最小值的中间值)
    uint32_t threshold_sum = 0;
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        threshold_sum += (sensor_max[i] + sensor_min[i]) / 2;
    }
    line_sensor.threshold = threshold_sum / LINE_SENSOR_COUNT;
    
    calibration_done = true;
}

/**
 * @brief 计算黑线位置
 * @return 黑线位置 (-3.5 到 +3.5)
 */
float calculate_line_position(void)
{
    uint32_t weighted_sum = 0;
    uint32_t total_weight = 0;
    
    // 加权平均算法计算黑线位置
    for(uint8_t i = 0; i < LINE_SENSOR_COUNT; i++)
    {
        if(line_sensor.digital_values[i])
        {
            // 传感器位置: 0->-3.5, 1->-2.5, ..., 7->+3.5
            int16_t position = (i * 1000) - 3500;  // 乘以1000避免浮点运算
            weighted_sum += position * line_sensor.raw_values[i];
            total_weight += line_sensor.raw_values[i];
        }
    }
    
    if(total_weight > 0)
    {
        line_sensor.line_position = (float)weighted_sum / (float)total_weight / 1000.0f;
    }
    
    return line_sensor.line_position;
}

/**
 * @brief 检测循迹状态
 * @return 当前状态
 */
LineState_t detect_line_state(void)
{
    uint32_t current_time = get_system_time_ms();
    
    // 检查是否丢线
    if(line_sensor.sensors_on_line == 0)
    {
        if((current_time - line_sensor.last_seen_time) > line_config.lost_timeout_ms)
        {
            line_sensor.state = LINE_STATE_LOST;
            return line_sensor.state;
        }
    }
    
    // 根据检测到的传感器数量和位置判断状态
    if(line_sensor.sensors_on_line >= 6)
    {
        // 大部分传感器检测到黑线 - 可能是十字路口或终点
        line_sensor.state = LINE_STATE_INTERSECTION;
    }
    else if(line_sensor.sensors_on_line >= 1 && line_sensor.sensors_on_line <= 3)
    {
        // 正常循迹状态
        if(line_sensor.line_position < -2.0f)
        {
            line_sensor.state = LINE_STATE_SHARP_LEFT;
        }
        else if(line_sensor.line_position > 2.0f)
        {
            line_sensor.state = LINE_STATE_SHARP_RIGHT;
        }
        else if(line_sensor.line_position < -1.0f)
        {
            line_sensor.state = LINE_STATE_LEFT_TURN;
        }
        else if(line_sensor.line_position > 1.0f)
        {
            line_sensor.state = LINE_STATE_RIGHT_TURN;
        }
        else
        {
            line_sensor.state = LINE_STATE_NORMAL;
        }
    }
    
    return line_sensor.state;
}

/**
 * @brief PID控制计算
 * @param setpoint 设定值 (通常为0)
 * @param measured_value 测量值 (黑线位置)
 * @return PID输出
 */
float line_pid_calculate(float setpoint, float measured_value)
{
    line_pid.error = setpoint - measured_value;
    
    // 积分项
    line_pid.integral += line_pid.error;
    
    // 积分限幅
    float max_integral = line_pid.max_output / line_pid.ki;
    if(line_pid.integral > max_integral)
        line_pid.integral = max_integral;
    else if(line_pid.integral < -max_integral)
        line_pid.integral = -max_integral;
    
    // 微分项
    line_pid.derivative = line_pid.error - line_pid.last_error;
    
    // PID输出计算
    line_pid.output = line_pid.kp * line_pid.error + 
                      line_pid.ki * line_pid.integral + 
                      line_pid.kd * line_pid.derivative;
    
    // 输出限幅
    if(line_pid.output > line_pid.max_output)
        line_pid.output = line_pid.max_output;
    else if(line_pid.output < -line_pid.max_output)
        line_pid.output = -line_pid.max_output;
    
    line_pid.last_error = line_pid.error;
    
    return line_pid.output;
}
