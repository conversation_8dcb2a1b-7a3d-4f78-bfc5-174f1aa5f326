/**
 * @file line_following_example.c
 * @brief 八路灰度传感器循迹控制示例程序
 * <AUTHOR>
 * @date 2024
 * 
 * 本示例展示如何使用八路灰度传感器实现2cm黑线循迹
 * 支持1米正方形轨道的直角转弯功能
 */

#include "line_following.h"
#include "adc.h"
#include "uart.h"
#include "multitimer.h"
#include <stdio.h>

/* 循迹任务定时器 */
static MultiTimer line_follow_timer;

/**
 * @brief 循迹系统示例主函数
 * 
 * 使用说明：
 * 1. 八路灰度传感器连接到ADC通道0-7
 * 2. 左右电机分别通过UART2和UART5控制
 * 3. 支持2cm宽黑线循迹，适用于1米正方形轨道
 * 4. 具备直角转弯检测和处理能力
 */
void line_following_example(void)
{
    uart_print("=== Line Following System Example ===\r\n");
    uart_print("Initializing system...\r\n");
    
    // 1. 初始化ADC (在main函数中已完成)
    // MX_ADC1_Init();
    
    // 2. 初始化循迹系统
    line_following_init();
    
    // 3. 启动ADC连续转换
    start_adc_conversion();
    uart_print("ADC conversion started\r\n");
    
    // 4. 传感器校准
    uart_print("Starting sensor calibration...\r\n");
    uart_print("Please place the robot on the track and move it over white and black areas\r\n");
    
    calibrate_line_sensors(5000); // 5秒校准时间
    
    // 5. 配置循迹参数
    uart_print("Configuring line following parameters...\r\n");
    
    // 根据2cm黑线和实际硬件调整参数
    set_line_follow_params(
        2.5f,   // KP - 比例系数，控制响应速度
        0.1f,   // KI - 积分系数，消除稳态误差
        0.8f,   // KD - 微分系数，减少超调
        600     // 基础速度，根据电机特性调整
    );
    
    // 配置系统参数
    line_config.enabled = 1;                // 启用循迹
    line_config.debug_mode = 1;             // 启用调试模式
    line_config.lost_timeout_ms = 2000;     // 丢线超时2秒
    line_config.turn_delay_ms = 300;        // 转弯延时300ms
    line_config.speed_factor = 0.8f;        // 速度因子80%
    
    uart_print("Line following parameters configured\r\n");
    
    // 6. 启动循迹任务定时器 (20ms周期)
    multiTimerStart(&line_follow_timer, 20, line_following_timer_callback, NULL);
    
    uart_print("Line following system started!\r\n");
    uart_print("Commands:\r\n");
    uart_print("  $L1 - Start line following\r\n");
    uart_print("  $L0 - Stop line following\r\n");
    uart_print("  $LS - Show sensor status\r\n");
    uart_print("  $LT - Show statistics\r\n");
    uart_print("  $LC - Calibrate sensors\r\n");
    uart_print("  $LP[kp,ki,kd,speed] - Set PID parameters\r\n");
    
    // 系统现在会自动运行循迹控制
    // 主循环在定时器回调中处理
}

/**
 * @brief 循迹定时器回调函数
 * @param timer 定时器指针
 * @param userData 用户数据
 */
void line_following_timer_callback(MultiTimer *timer, void *userData)
{
    // 执行循迹控制
    line_following_task();
    
    // 重启定时器
    multiTimerStart(timer, 20, line_following_timer_callback, NULL);
}

/**
 * @brief 处理循迹相关的串口命令
 * @param data 命令数据
 * @param len 数据长度
 */
void process_line_follow_commands(uint8_t *data, uint16_t len)
{
    if(len < 2 || data[0] != '$' || data[1] != 'L')
        return;
    
    switch(data[2])
    {
        case '1': // 启动循迹
            line_config.enabled = 1;
            uart_print("Line following started\r\n");
            break;
            
        case '0': // 停止循迹
            line_config.enabled = 0;
            stop_motors();
            uart_print("Line following stopped\r\n");
            break;
            
        case 'S': // 显示传感器状态
        case 's':
            {
                char status_str[512];
                get_line_sensor_status(status_str, sizeof(status_str));
                uart_print("%s", status_str);
            }
            break;
            
        case 'T': // 显示统计信息
        case 't':
            {
                char stats_str[512];
                get_line_follow_stats(stats_str, sizeof(stats_str));
                uart_print("%s", stats_str);
            }
            break;
            
        case 'C': // 重新校准传感器
        case 'c':
            line_config.enabled = 0; // 停止循迹
            stop_motors();
            uart_print("Starting recalibration...\r\n");
            calibrate_line_sensors(5000);
            uart_print("Recalibration complete\r\n");
            break;
            
        case 'P': // 设置PID参数
        case 'p':
            if(len >= 20) // 简单的参数解析
            {
                // 格式: $LP[kp,ki,kd,speed]
                // 这里简化处理，实际应用中可以实现更完善的参数解析
                float kp = 2.0f, ki = 0.1f, kd = 0.5f;
                uint16_t speed = 600;
                
                // 简单解析 (实际应用中建议使用更robust的解析方法)
                sscanf((char*)&data[3], "%f,%f,%f,%hu", &kp, &ki, &kd, &speed);
                
                set_line_follow_params(kp, ki, kd, speed);
            }
            break;
            
        case 'D': // 切换调试模式
        case 'd':
            line_config.debug_mode = !line_config.debug_mode;
            uart_print("Debug mode: %s\r\n", line_config.debug_mode ? "ON" : "OFF");
            break;
            
        default:
            uart_print("Unknown line following command: %c\r\n", data[2]);
            break;
    }
}

/**
 * @brief 循迹系统硬件连接说明
 * 
 * 八路灰度传感器连接:
 * - 传感器0: PA0 (ADC1_IN0)
 * - 传感器1: PA1 (ADC1_IN1)
 * - 传感器2: PA2 (ADC1_IN2) - 注意与UART2冲突
 * - 传感器3: PA3 (ADC1_IN3) - 注意与UART2冲突
 * - 传感器4: PC0 (ADC1_IN10) - 替代PA4避免SPI冲突
 * - 传感器5: PC1 (ADC1_IN11) - 替代PA5避免SPI冲突
 * - 传感器6: PC2 (ADC1_IN12) - 替代PA6避免SPI冲突
 * - 传感器7: PC3 (ADC1_IN13) - 替代PA7避免SPI冲突
 * 
 * 电机连接:
 * - 左电机: UART2 (PA2/PA3) - 地址0x01
 * - 右电机: UART5 - 地址0x01
 * 
 * 注意事项:
 * 1. 如果PA2/PA3被UART2占用，需要调整传感器引脚分配
 * 2. 传感器供电电压应与MCU ADC参考电压匹配
 * 3. 建议在传感器输入端添加滤波电容减少噪声
 * 4. 传感器安装高度应保持一致，距离地面2-5mm
 */

/**
 * @brief 循迹算法原理说明
 * 
 * 1. 传感器布局:
 *    [0][1][2][3][4][5][6][7]
 *    传感器间距10mm，总宽度70mm，适合2cm黑线检测
 * 
 * 2. 位置计算:
 *    使用加权平均算法计算黑线位置
 *    位置范围: -3.5 到 +3.5 (对应传感器0到7)
 *    位置0表示黑线在中心位置
 * 
 * 3. 状态检测:
 *    - NORMAL: 1-3个传感器检测到黑线，正常循迹
 *    - LEFT_TURN/RIGHT_TURN: 黑线偏向一侧，需要转弯
 *    - SHARP_LEFT/SHARP_RIGHT: 急转弯，需要原地转向
 *    - INTERSECTION: 多个传感器检测到黑线，十字路口
 *    - LOST: 没有传感器检测到黑线，丢线状态
 * 
 * 4. PID控制:
 *    - 设定值: 0 (黑线在中心)
 *    - 测量值: 计算得到的黑线位置
 *    - 输出: 左右电机速度差
 * 
 * 5. 转弯处理:
 *    - 检测到直角转弯时，暂停PID控制
 *    - 执行原地转向动作
 *    - 转向完成后恢复PID控制
 * 
 * 6. 参数调优建议:
 *    - KP: 控制响应速度，过大会振荡，过小响应慢
 *    - KI: 消除稳态误差，通常设置较小值
 *    - KD: 减少超调，提高稳定性
 *    - 基础速度: 根据轨道复杂度和精度要求调整
 */

/**
 * @brief 故障排除指南
 * 
 * 常见问题及解决方法:
 * 
 * 1. 传感器读数异常:
 *    - 检查ADC配置和引脚连接
 *    - 确认传感器供电正常
 *    - 重新校准传感器
 * 
 * 2. 循迹不稳定:
 *    - 调整PID参数，特别是KP和KD
 *    - 检查传感器安装高度和角度
 *    - 降低基础速度
 * 
 * 3. 转弯不准确:
 *    - 调整转弯延时参数
 *    - 检查左右电机速度是否匹配
 *    - 调整急转弯检测阈值
 * 
 * 4. 频繁丢线:
 *    - 检查黑线对比度
 *    - 调整检测阈值
 *    - 增加丢线超时时间
 * 
 * 5. 电机不响应:
 *    - 检查串口连接和波特率
 *    - 确认电机地址设置
 *    - 检查电机驱动器状态
 */
