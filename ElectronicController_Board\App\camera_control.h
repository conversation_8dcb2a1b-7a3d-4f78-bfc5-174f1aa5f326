/**
 * @file camera_control.h
 * @brief 摄像头控制和数据处理头文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef __CAMERA_CONTROL_H
#define __CAMERA_CONTROL_H

#include "main.h"
#include "usart.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 摄像头参数定义 */
#define CAM_WIDTH 320                 // 摄像头图像宽度
#define CAM_HEIGHT 240                // 摄像头图像高度
#define CAM_THRESHOLD 128             // 摄像头图像二值化阈值
#define CAM_MAX_FRAME_SIZE 512        // 最大帧大小

/* 摄像头通信协议定义 */
#define CAM_FRAME_HEADER1 0xFF        // 帧头1
#define CAM_FRAME_HEADER2 0xA5        // 帧头2
#define CAM_CMD_RECT_DATA 0x01        // 矩形数据命令
#define CAM_CMD_IMAGE_DATA 0x02       // 图像数据命令
#define CAM_CMD_STATUS 0x03           // 状态命令
#define CAM_CMD_SET_THRESHOLD 0x04    // 设置阈值命令
#define CAM_CMD_SET_ROI 0x05          // 设置感兴趣区域命令

/* 云台控制参数 */
#define GIMBAL_DEAD_ZONE_X 5          // X轴死区（像素）
#define GIMBAL_DEAD_ZONE_Y 5          // Y轴死区（像素）
#define GIMBAL_PIXEL_TO_STEP_X 10.0f  // X轴像素到步数的转换比例
#define GIMBAL_PIXEL_TO_STEP_Y 10.0f  // Y轴像素到步数的转换比例
#define GIMBAL_MAX_STEP_PER_CYCLE 1000 // 单次最大调整步数

/**
 * @brief 摄像头数据结构
 */
typedef struct
{
    uint8_t frame_buffer[CAM_WIDTH * CAM_HEIGHT / 8]; // 二值化后的图像缓冲
    int16_t center_x;                                 // 检测到的目标中心X坐标
    int16_t center_y;                                 // 检测到的目标中心Y坐标
    int16_t rect_x;                                   // 矩形左上角X坐标
    int16_t rect_y;                                   // 矩形左上角Y坐标
    int16_t rect_width;                               // 矩形宽度
    int16_t rect_height;                              // 矩形高度
    uint8_t target_found;                             // 是否检测到目标
    uint8_t confidence;                               // 检测置信度(0-100)
    uint32_t frame_id;                                // 帧ID，用于数据同步
} Camera_t;

/**
 * @brief 摄像头数据帧结构
 */
typedef struct __attribute__((packed))
{
    uint8_t header1;      // 帧头1 (0xFF)
    uint8_t header2;      // 帧头2 (0xA5)
    uint8_t cmd;          // 命令类型
    uint8_t length;       // 数据长度
    uint8_t data[0];      // 数据内容
} CameraFrame_t;

/**
 * @brief 矩形数据结构
 */
typedef struct __attribute__((packed))
{
    int16_t center_x;     // 矩形中心X坐标
    int16_t center_y;     // 矩形中心Y坐标
    int16_t rect_x;       // 矩形左上角X坐标
    int16_t rect_y;       // 矩形左上角Y坐标
    int16_t width;        // 矩形宽度
    int16_t height;       // 矩形高度
    uint8_t confidence;   // 检测置信度
    uint32_t frame_id;    // 帧ID
    uint8_t checksum;     // 校验和
} RectData_t;

/**
 * @brief 云台控制参数结构
 */
typedef struct
{
    float pixel_to_step_x;    // X轴像素到步数转换比例
    float pixel_to_step_y;    // Y轴像素到步数转换比例
    int16_t dead_zone_x;      // X轴死区
    int16_t dead_zone_y;      // Y轴死区
    int32_t max_step_per_cycle; // 单次最大调整步数
    uint8_t confidence_threshold; // 置信度阈值
} GimbalControl_t;

/* 外部变量声明 */
extern Camera_t camera_data;
extern GimbalControl_t gimbal_control;

/* 函数声明 */

/**
 * @brief 计算帧校验和
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和
 */
uint8_t calculate_frame_checksum(uint8_t *data, uint16_t len);

/**
 * @brief 处理矩形检测数据
 * @param rect_data 矩形数据指针
 * @return 处理结果 0-成功 1-失败
 */
uint8_t process_rect_data(RectData_t *rect_data);

/**
 * @brief 处理接收到的摄像头数据
 * @param data 接收到的数据
 * @param len 数据长度
 */
void process_camera_data(uint8_t *data, uint16_t len);

/**
 * @brief 自动跟踪控制
 */
void auto_track_control(void);

/**
 * @brief 发送命令到摄像头
 * @param cmd 命令类型
 * @param data 数据指针
 * @param len 数据长度
 * @return 发送结果 0-成功 1-失败
 */
uint8_t send_camera_command(uint8_t cmd, uint8_t *data, uint8_t len);

/**
 * @brief 设置摄像头阈值
 * @param threshold 阈值(0-255)
 * @return 设置结果 0-成功 1-失败
 */
uint8_t set_camera_threshold(uint8_t threshold);

/**
 * @brief 设置摄像头感兴趣区域
 * @param x 左上角X坐标
 * @param y 左上角Y坐标
 * @param width 宽度
 * @param height 高度
 * @return 设置结果 0-成功 1-失败
 */
uint8_t set_camera_roi(int16_t x, int16_t y, int16_t width, int16_t height);

/**
 * @brief 初始化云台控制参数
 */
void init_gimbal_control(void);

/**
 * @brief 获取摄像头状态信息
 * @param status_str 状态字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void get_camera_status(char *status_str, uint16_t max_len);

#ifdef __cplusplus
}
#endif

#endif /* __CAMERA_CONTROL_H */
