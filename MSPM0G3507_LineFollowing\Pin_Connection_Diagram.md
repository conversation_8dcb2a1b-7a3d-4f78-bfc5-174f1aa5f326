# MSPM0G3507与八路灰度传感器连接图

## 🔌 详细连接方案

### 传感器引脚连接

```
八路灰度传感器模块              MSPM0G3507立创天空星开发板
┌──────────────────┐         ┌─────────────────────────┐
│ GND              │ ←────── │ GND                     │
│ 5V               │ ←────── │ 5V (外部电源)            │
│ OUT              │ ←────── │ PA1  (A01引脚)          │
│ EN               │ ←────── │ PA26                    │
│ AD2              │ ←────── │ PB21 (B21引脚)          │
│ AD1              │ ←────── │ PA9  (A09引脚)          │
│ AD0              │ ←────── │ PB6  (B06引脚)          │
│ ERR              │ ←────── │ PA16                    │
└──────────────────┘         └─────────────────────────┘
```

### 电机驱动连接

```
左电机驱动器                    MSPM0G3507开发板
┌──────────────┐             ┌─────────────────┐
│ PWM1         │ ←────────── │ PB20            │
│ PWM2         │ ←────────── │ PB13            │
│ DIR1         │ ←────────── │ PB19            │
│ DIR2         │ ←────────── │ PB12            │
│ VCC          │ ←────────── │ 5V/12V          │
│ GND          │ ←────────── │ GND             │
└──────────────┘             └─────────────────┘

右电机驱动器                    MSPM0G3507开发板
┌──────────────┐             ┌─────────────────┐
│ PWM1         │ ←────────── │ PB4             │
│ PWM2         │ ←────────── │ PB1             │
│ DIR1         │ ←────────── │ PB3             │
│ DIR2         │ ←────────── │ PB0             │
│ VCC          │ ←────────── │ 5V/12V          │
│ GND          │ ←────────── │ GND             │
└──────────────┘             └─────────────────┘
```

### 串口调试连接

```
USB转TTL模块                   MSPM0G3507开发板
┌──────────────┐             ┌─────────────────┐
│ TX           │ ←────────── │ PA11 (RX)       │
│ RX           │ ←────────── │ PA10 (TX)       │
│ GND          │ ←────────── │ GND             │
│ VCC          │             │ (不连接)         │
└──────────────┘             └─────────────────┘
```

## 📋 引脚功能对照表

| 传感器功能 | 传感器引脚 | MSPM0G3507引脚 | 开发板标号 | GPIO配置 | 说明 |
|-----------|-----------|---------------|-----------|----------|------|
| 电源地 | GND | GND | GND | - | 公共地 |
| 电源正 | 5V | 5V | 5V | - | 5V供电 |
| 数字输出 | OUT | PA1 | A01 | Input | 传感器数据输出 |
| 使能控制 | EN | PA26 | - | Output | 传感器使能 |
| 地址位2 | AD2 | PB21 | B21 | Output | 传感器地址选择 |
| 地址位1 | AD1 | PA9 | A09 | Output | 传感器地址选择 |
| 地址位0 | AD0 | PB6 | B06 | Output | 传感器地址选择 |
| 错误检测 | ERR | PA16 | - | Input | 错误状态监控 |

## 🔧 物理连接注意事项

### 1. 电源连接
- **传感器供电**: 必须使用5V电源
- **开发板供电**: 可以使用USB或外部电源
- **共地连接**: 确保所有设备共地

### 2. 信号线连接
- 使用杜邦线连接信号线
- 保持连接线尽可能短
- 避免信号线与电源线并行走线

### 3. 电机驱动连接
- 检查电机驱动器的逻辑电平要求
- 确保PWM信号电平匹配
- 电机电源与逻辑电源分离

## ⚡ 电源系统设计

### 1. 推荐电源方案

```
外部12V电源
    │
    ├── 12V → 电机驱动器 → 电机
    │
    ├── 12V → 5V降压模块 → 传感器模块
    │
    └── 12V → 3.3V降压模块 → MSPM0G3507开发板
```

### 2. 电流需求估算

| 设备 | 电压 | 电流 | 功率 |
|------|------|------|------|
| MSPM0G3507 | 3.3V | 50mA | 0.17W |
| 传感器模块 | 5V | 100mA | 0.5W |
| 电机驱动器 | 12V | 2A | 24W |
| **总计** | - | - | **约25W** |

### 3. 电源连接示意图

```
12V电源适配器
    │
    ├─ 降压模块1 (12V→5V, 1A) ─── 传感器模块
    │
    ├─ 降压模块2 (12V→3.3V, 0.5A) ─── MSPM0G3507
    │
    └─ 直接供电 (12V, 3A) ─── 电机驱动器
```

## 🔍 连接验证方法

### 1. 电源测试
```c
// 检查各电源电压
// 5V电源: 4.8V - 5.2V
// 3.3V电源: 3.1V - 3.5V
```

### 2. GPIO测试
```c
// 测试输出引脚
DL_GPIO_setPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN);   // 设置高电平
DL_GPIO_clearPins(SENSOR_AD0_PORT, SENSOR_AD0_PIN); // 设置低电平

// 测试输入引脚
uint32_t pin_state = DL_GPIO_readPins(SENSOR_OUT_PORT, SENSOR_OUT_PIN);
```

### 3. 传感器通信测试
```c
// 测试传感器读取
for(uint8_t i = 0; i < 8; i++) {
    uint8_t value = read_single_sensor(i);
    printf("Sensor %d: %d\n", i, value);
}
```

## 📐 PCB布局建议

### 1. 布线原则
- 数字信号线远离电源线
- 地线尽可能宽
- 高频信号线短而直

### 2. 去耦电容
- 每个IC附近放置100nF陶瓷电容
- 电源输入端放置10μF电解电容

### 3. 连接器选择
- 使用可靠的连接器
- 考虑防反接设计
- 预留测试点

## ⚠️ 安全注意事项

1. **电源极性**: 严格检查正负极，避免反接
2. **电压匹配**: 确保各模块电压要求匹配
3. **电流容量**: 确保电源能提供足够电流
4. **散热设计**: 大功率器件需要散热
5. **短路保护**: 建议添加保险丝保护

## 🔧 故障排除

### 常见问题及解决方法

1. **传感器无响应**
   - 检查5V电源是否正常
   - 验证EN引脚是否正确控制
   - 检查地址线连接

2. **读取数据异常**
   - 检查OUT引脚连接
   - 验证时序是否正确
   - 检查ERR引脚状态

3. **电机不转**
   - 检查PWM信号输出
   - 验证方向控制引脚
   - 检查电机驱动器电源

---
*连接完成后，请按照软件配置文档进行SysConfig配置*
