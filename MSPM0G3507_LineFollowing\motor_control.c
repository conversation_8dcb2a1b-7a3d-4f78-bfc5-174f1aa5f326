/**
 * @file motor_control.c
 * @brief MSPM0G3507电机控制实现文件
 * <AUTHOR>
 * @date 2024
 */

#include "line_following.h"
#include "ti/driverlib/dl_timer.h"
#include "ti/driverlib/dl_gpio.h"

/* 电机引脚定义 */
#define LEFT_MOTOR_PWM1_PORT    GPIOB
#define LEFT_MOTOR_PWM1_PIN     DL_GPIO_PIN_20
#define LEFT_MOTOR_PWM2_PORT    GPIOB
#define LEFT_MOTOR_PWM2_PIN     DL_GPIO_PIN_13
#define LEFT_MOTOR_DIR1_PORT    GPIOB
#define LEFT_MOTOR_DIR1_PIN     DL_GPIO_PIN_19
#define LEFT_MOTOR_DIR2_PORT    GPIOB
#define LEFT_MOTOR_DIR2_PIN     DL_GPIO_PIN_12

#define RIGHT_MOTOR_PWM1_PORT   GPIOB
#define RIGHT_MOTOR_PWM1_PIN    DL_GPIO_PIN_4
#define RIGHT_MOTOR_PWM2_PORT   GPIOB
#define RIGHT_MOTOR_PWM2_PIN    DL_GPIO_PIN_1
#define RIGHT_MOTOR_DIR1_PORT   GPIOB
#define RIGHT_MOTOR_DIR1_PIN    DL_GPIO_PIN_3
#define RIGHT_MOTOR_DIR2_PORT   GPIOB
#define RIGHT_MOTOR_DIR2_PIN    DL_GPIO_PIN_0

/**
 * @brief 电机控制
 * @param pid_output PID输出值
 */
void control_motors(float pid_output)
{
    // 基础速度调整
    int16_t base_speed = (int16_t)(motor_control.base_speed * line_config.speed_factor);
    
    // 计算左右电机速度
    motor_control.left_speed = base_speed - (int16_t)pid_output;
    motor_control.right_speed = base_speed + (int16_t)pid_output;
    
    // 速度限制
    if(motor_control.left_speed > MOTOR_MAX_SPEED)
        motor_control.left_speed = MOTOR_MAX_SPEED;
    else if(motor_control.left_speed < -MOTOR_MAX_SPEED)
        motor_control.left_speed = -MOTOR_MAX_SPEED;
        
    if(motor_control.right_speed > MOTOR_MAX_SPEED)
        motor_control.right_speed = MOTOR_MAX_SPEED;
    else if(motor_control.right_speed < -MOTOR_MAX_SPEED)
        motor_control.right_speed = -MOTOR_MAX_SPEED;
    
    // 发送电机控制命令
    set_motor_speeds(motor_control.left_speed, motor_control.right_speed);
}

/**
 * @brief 停止电机
 */
void stop_motors(void)
{
    motor_control.left_speed = 0;
    motor_control.right_speed = 0;
    set_motor_speeds(0, 0);
}

/**
 * @brief 设置电机速度
 * @param left_speed 左电机速度 (-100到+100)
 * @param right_speed 右电机速度 (-100到+100)
 */
void set_motor_speeds(int16_t left_speed, int16_t right_speed)
{
    // 限制速度范围
    if(left_speed > 100) left_speed = 100;
    if(left_speed < -100) left_speed = -100;
    if(right_speed > 100) right_speed = 100;
    if(right_speed < -100) right_speed = -100;
    
    // 左电机控制
    if(left_speed >= 0)
    {
        // 正转
        DL_GPIO_clearPins(LEFT_MOTOR_DIR1_PORT, LEFT_MOTOR_DIR1_PIN);
        DL_GPIO_setPins(LEFT_MOTOR_DIR2_PORT, LEFT_MOTOR_DIR2_PIN);
        
        // 设置PWM占空比
        uint32_t pwm_value = (uint32_t)(left_speed * 1000 / 100); // 转换为0-1000
        DL_Timer_setCaptureCompareValue(PWM_0_INST, pwm_value, DL_TIMER_CC_0_INDEX);
    }
    else
    {
        // 反转
        DL_GPIO_setPins(LEFT_MOTOR_DIR1_PORT, LEFT_MOTOR_DIR1_PIN);
        DL_GPIO_clearPins(LEFT_MOTOR_DIR2_PORT, LEFT_MOTOR_DIR2_PIN);
        
        // 设置PWM占空比
        uint32_t pwm_value = (uint32_t)((-left_speed) * 1000 / 100);
        DL_Timer_setCaptureCompareValue(PWM_0_INST, pwm_value, DL_TIMER_CC_0_INDEX);
    }
    
    // 右电机控制
    if(right_speed >= 0)
    {
        // 正转
        DL_GPIO_clearPins(RIGHT_MOTOR_DIR1_PORT, RIGHT_MOTOR_DIR1_PIN);
        DL_GPIO_setPins(RIGHT_MOTOR_DIR2_PORT, RIGHT_MOTOR_DIR2_PIN);
        
        // 设置PWM占空比
        uint32_t pwm_value = (uint32_t)(right_speed * 1000 / 100);
        DL_Timer_setCaptureCompareValue(PWM_1_INST, pwm_value, DL_TIMER_CC_0_INDEX);
    }
    else
    {
        // 反转
        DL_GPIO_setPins(RIGHT_MOTOR_DIR1_PORT, RIGHT_MOTOR_DIR1_PIN);
        DL_GPIO_clearPins(RIGHT_MOTOR_DIR2_PORT, RIGHT_MOTOR_DIR2_PIN);
        
        // 设置PWM占空比
        uint32_t pwm_value = (uint32_t)((-right_speed) * 1000 / 100);
        DL_Timer_setCaptureCompareValue(PWM_1_INST, pwm_value, DL_TIMER_CC_0_INDEX);
    }
}

/**
 * @brief 处理特殊状态 (转弯、路口等)
 */
void handle_special_states(void)
{
    static uint32_t state_start_time = 0;
    uint32_t current_time = get_system_time_ms();
    
    switch(line_sensor.state)
    {
        case LINE_STATE_LOST:
            // 丢线处理 - 停止
            stop_motors();
            break;
            
        case LINE_STATE_INTERSECTION:
            // 十字路口处理 - 直行通过
            if(state_start_time == 0)
            {
                state_start_time = current_time;
            }
            
            // 直行一段时间后恢复正常循迹
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                line_sensor.state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            else
            {
                // 保持直行
                set_motor_speeds(motor_control.base_speed, motor_control.base_speed);
            }
            break;
            
        case LINE_STATE_SHARP_LEFT:
            // 急左转处理
            if(state_start_time == 0)
            {
                state_start_time = current_time;
            }
            
            // 原地左转
            int16_t turn_speed = (int16_t)(motor_control.base_speed * MOTOR_TURN_SPEED_RATIO);
            set_motor_speeds(-turn_speed, turn_speed);
            
            // 转弯延时后恢复PID控制
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                line_sensor.state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            break;
            
        case LINE_STATE_SHARP_RIGHT:
            // 急右转处理
            if(state_start_time == 0)
            {
                state_start_time = current_time;
            }
            
            // 原地右转
            turn_speed = (int16_t)(motor_control.base_speed * MOTOR_TURN_SPEED_RATIO);
            set_motor_speeds(turn_speed, -turn_speed);
            
            // 转弯延时后恢复PID控制
            if((current_time - state_start_time) > line_config.turn_delay_ms)
            {
                line_sensor.state = LINE_STATE_NORMAL;
                state_start_time = 0;
            }
            break;
            
        case LINE_STATE_END:
            // 终点处理
            stop_motors();
            line_config.enabled = false;
            break;
            
        case LINE_STATE_STOP:
            // 停止状态
            stop_motors();
            break;
            
        default:
            // 正常状态和普通转弯由PID控制处理
            state_start_time = 0;
            break;
    }
}

/**
 * @brief 循迹主控制函数
 */
void line_following_control(void)
{
    if(!line_config.enabled)
    {
        stop_motors();
        return;
    }
    
    // 读取传感器数据
    read_line_sensors();
    
    // 计算黑线位置
    calculate_line_position();
    
    // 检测当前状态
    detect_line_state();
    
    // 根据状态进行控制
    if(line_sensor.state == LINE_STATE_NORMAL || 
       line_sensor.state == LINE_STATE_LEFT_TURN || 
       line_sensor.state == LINE_STATE_RIGHT_TURN)
    {
        // 正常PID控制
        float pid_output = line_pid_calculate(0.0f, line_sensor.line_position);
        control_motors(pid_output);
    }
    else
    {
        // 特殊状态处理
        handle_special_states();
    }
}

/**
 * @brief 循迹任务函数
 */
void line_following_task(void)
{
    // 执行循迹控制
    line_following_control();
}

/**
 * @brief 获取系统时间(毫秒)
 */
uint32_t get_system_time_ms(void)
{
    // 这里需要根据实际的定时器配置来实现
    // 假设使用32MHz时钟，每32000个时钟周期为1ms
    static uint32_t last_cycles = 0;
    static uint32_t time_ms = 0;
    
    uint32_t current_cycles = DL_Timer_getTimerCount(TIMER_0_INST);
    
    if(current_cycles >= last_cycles)
    {
        if((current_cycles - last_cycles) >= 32000)
        {
            time_ms += (current_cycles - last_cycles) / 32000;
            last_cycles = current_cycles;
        }
    }
    else
    {
        // 定时器溢出处理
        uint32_t overflow_cycles = (0xFFFFFFFF - last_cycles) + current_cycles;
        time_ms += overflow_cycles / 32000;
        last_cycles = current_cycles;
    }
    
    return time_ms;
}
