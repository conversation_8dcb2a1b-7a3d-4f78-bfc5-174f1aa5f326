/**
 * @file      : project_core.c
 * @brief     : 电子控制板核心代码 - 论文附录
 * @copyright : 米醋电子工作室
 */

/* 包含头文件 */
#include "stm32f4xx_hal.h"
#include "ssd1306.h"
#include "multitimer.h"
#include "motor.h"
#include "emm_v5.h"
#include "rt_ringbuffer.h"
#include "pid.h"
#include "flash.h"
#include "camera.h"
#include "usart.h"
#include "dma.h"
#include "gpio.h"
#include "spi.h"
#include "delay.h"
#include "position.h"
#include "camera_control.h"
#include "adc.h"
#include "line_following.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

/* 全局定义 */
#define OLED_TASK_TIME 500  // OLED刷新时间间隔(ms)
#define MOTOR_TASK_TIME 20  // 电机控制时间间隔(ms)
#define PID_TASK_TIME 10    // PID计算时间间隔(ms)
#define CAMERA_TASK_TIME 50     // 摄像头数据处理间隔(ms)
#define USER_TASK_TIME 100      // 用户交互时间间隔(ms)
#define LINE_FOLLOW_TASK_TIME 20 // 循迹控制时间间隔(ms)
#define UART_TIMEOUT 100        // 串口超时时间(ms)

#define X_MOTOR_ADDR 0x01        // X轴电机地址
#define Y_MOTOR_ADDR 0x01        // Y轴电机地址
#define MAX_POSITION_X 10000     // X轴最大位置
#define MAX_POSITION_Y 10000     // Y轴最大位置
#define MOTOR_SPEED_DEFAULT 1000 // 默认电机速度
#define MOTOR_ACC_DEFAULT 500    // 默认电机加速度

#define PID_KP 1.5f         // PID比例系数
#define PID_KI 0.05f        // PID积分系数
#define PID_KD 0.2f         // PID微分系数
#define PID_MAX_OUTPUT 5000 // PID最大输出

#define FLASH_SECTOR FLASH_SECTOR_11 // Flash存储扇区
#define FLASH_ADDRESS 0x080E0000     // Flash存储地址

#define CAM_THRESHOLD 128 // 摄像头图像二值化阈值
#define CAM_WIDTH 320     // 摄像头图像宽度
#define CAM_HEIGHT 240    // 摄像头图像高度

/* 全局变量定义 */
// 多任务定时器
MultiTimer mt_system, mt_oled, mt_usart, mt_cam, mt_pid, mt_user, mt_line_follow;

// 环形缓冲区
struct rt_ringbuffer ringbuffer_x, ringbuffer_y, ringbuffer_cam, ringbuffer_user;
uint8_t ringbuffer_pool_x[256], ringbuffer_pool_y[256];
uint8_t ringbuffer_pool_cam[512], ringbuffer_pool_user[256];

// 系统状态变量
__IO uint32_t count = 0;
char buffer[20];

// 电机相关变量
typedef struct
{
    int32_t current_pos; // 当前位置
    int32_t target_pos;  // 目标位置
    int32_t initial_pos; // 初始位置
    uint16_t speed;      // 速度
    uint16_t acc;        // 加速度
    uint8_t is_running;  // 运行状态
} MotorParam_t;

MotorParam_t motor_x, motor_y;

// PID控制器
typedef struct
{
    float kp;         // 比例系数
    float ki;         // 积分系数
    float kd;         // 微分系数
    float error;      // 当前误差
    float last_error; // 上次误差
    float integral;   // 积分值
    float output;     // 输出值
    float max_output; // 最大输出限制
} PID_t;

PID_t pid_x, pid_y;

// 摄像头数据处理变量
typedef struct
{
    uint8_t frame_buffer[CAM_WIDTH * CAM_HEIGHT / 8]; // 二值化后的图像缓冲
    int16_t center_x;                                 // 检测到的目标中心X坐标
    int16_t center_y;                                 // 检测到的目标中心Y坐标
    int16_t rect_x;                                   // 矩形左上角X坐标
    int16_t rect_y;                                   // 矩形左上角Y坐标
    int16_t rect_width;                               // 矩形宽度
    int16_t rect_height;                              // 矩形高度
    uint8_t target_found;                             // 是否检测到目标
    uint8_t confidence;                               // 检测置信度(0-100)
    uint32_t frame_id;                                // 帧ID，用于数据同步
} Camera_t;

// 摄像头通信协议定义
#define CAM_FRAME_HEADER1 0xFF    // 帧头1
#define CAM_FRAME_HEADER2 0xA5    // 帧头2
#define CAM_CMD_RECT_DATA 0x01    // 矩形数据命令
#define CAM_CMD_IMAGE_DATA 0x02   // 图像数据命令
#define CAM_CMD_STATUS 0x03       // 状态命令
#define CAM_MAX_FRAME_SIZE 512    // 最大帧大小

// 摄像头数据帧结构
typedef struct __attribute__((packed))
{
    uint8_t header1;      // 帧头1 (0xFF)
    uint8_t header2;      // 帧头2 (0xA5)
    uint8_t cmd;          // 命令类型
    uint8_t length;       // 数据长度
    uint8_t data[0];      // 数据内容
} CameraFrame_t;

// 矩形数据结构
typedef struct __attribute__((packed))
{
    int16_t center_x;     // 矩形中心X坐标
    int16_t center_y;     // 矩形中心Y坐标
    int16_t rect_x;       // 矩形左上角X坐标
    int16_t rect_y;       // 矩形左上角Y坐标
    int16_t width;        // 矩形宽度
    int16_t height;       // 矩形高度
    uint8_t confidence;   // 检测置信度
    uint32_t frame_id;    // 帧ID
    uint8_t checksum;     // 校验和
} RectData_t;

Camera_t camera_data;

// 系统配置
typedef struct
{
    uint32_t magic_number; // 魔术数字，用于验证配置有效性
    MotorParam_t saved_x;  // 保存的X电机参数
    MotorParam_t saved_y;  // 保存的Y电机参数
    uint8_t mode;          // 系统工作模式
    uint8_t auto_track;    // 自动跟踪使能标志
    uint16_t checksum;     // 校验和
} SystemConfig_t;

SystemConfig_t sys_config = {
    .magic_number = 0xA5A5A5A5,
    .mode = 0,
    .auto_track = 0};

/**
 * @brief 获取系统时钟,为多定时器提供基准
 */
uint64_t bsp_get_systick(void)
{
    return (uint64_t)uwTick;
}

/**
 * @brief 计算PID控制输出
 * @param pid PID控制器结构体
 * @param current 当前值
 * @param target 目标值
 * @return 计算后的PID输出值
 */
float calculate_pid(PID_t *pid, float current, float target)
{
    pid->error = target - current;
    pid->integral += pid->error;

    // 积分限幅
    if (pid->integral > pid->max_output)
        pid->integral = pid->max_output;
    else if (pid->integral < -pid->max_output)
        pid->integral = -pid->max_output;

    // 计算PID输出
    pid->output = pid->kp * pid->error +
                  pid->ki * pid->integral +
                  pid->kd * (pid->error - pid->last_error);

    // 输出限幅
    if (pid->output > pid->max_output)
        pid->output = pid->max_output;
    else if (pid->output < -pid->max_output)
        pid->output = -pid->max_output;

    pid->last_error = pid->error;

    return pid->output;
}

/**
 * @brief 初始化PID控制器
 * @param pid PID控制器指针
 */
void init_pid(PID_t *pid)
{
    pid->kp = PID_KP;
    pid->ki = PID_KI;
    pid->kd = PID_KD;
    pid->error = 0;
    pid->last_error = 0;
    pid->integral = 0;
    pid->output = 0;
    pid->max_output = PID_MAX_OUTPUT;
}

/**
 * @brief 控制电机运动到指定位置
 * @param huart 串口句柄
 * @param addr 电机地址
 * @param pos 目标位置
 * @param speed 运动速度
 * @param acc 加速度
 */
void motor_move_to_position(UART_HandleTypeDef *huart, uint8_t addr, int32_t pos, uint16_t speed, uint16_t acc)
{
    // 发送电机运动指令
    uint8_t cmd_buffer[20];
    uint8_t len = 0;

    // 命令头
    cmd_buffer[len++] = 0xAA; // 起始字节
    cmd_buffer[len++] = addr; // 地址
    cmd_buffer[len++] = 0x10; // 功能码:绝对定位

    // 位置参数(4字节)
    cmd_buffer[len++] = (pos >> 0) & 0xFF;
    cmd_buffer[len++] = (pos >> 8) & 0xFF;
    cmd_buffer[len++] = (pos >> 16) & 0xFF;
    cmd_buffer[len++] = (pos >> 24) & 0xFF;

    // 速度参数(2字节)
    cmd_buffer[len++] = (speed >> 0) & 0xFF;
    cmd_buffer[len++] = (speed >> 8) & 0xFF;

    // 加速度参数(2字节)
    cmd_buffer[len++] = (acc >> 0) & 0xFF;
    cmd_buffer[len++] = (acc >> 8) & 0xFF;

    // 计算校验和
    uint8_t checksum = 0;
    for (int i = 0; i < len; i++)
    {
        checksum += cmd_buffer[i];
    }
    cmd_buffer[len++] = checksum;

    // 发送命令
    HAL_UART_Transmit(huart, cmd_buffer, len, UART_TIMEOUT);
}

/**
 * @brief 读取电机位置
 * @param huart 串口句柄
 * @param addr 电机地址
 * @return 电机当前位置
 */
int32_t read_motor_position(UART_HandleTypeDef *huart, uint8_t addr)
{
    uint8_t cmd_buffer[5];
    uint8_t recv_buffer[10];
    int32_t position = 0;

    // 构建读取位置命令
    cmd_buffer[0] = 0xAA;                                          // 起始字节
    cmd_buffer[1] = addr;                                          // 地址
    cmd_buffer[2] = 0x20;                                          // 功能码:读取位置
    cmd_buffer[3] = cmd_buffer[0] + cmd_buffer[1] + cmd_buffer[2]; // 校验和

    // 发送命令
    HAL_UART_Transmit(huart, cmd_buffer, 4, UART_TIMEOUT);

    // 接收响应
    if (HAL_UART_Receive(huart, recv_buffer, 9, UART_TIMEOUT) == HAL_OK)
    {
        // 解析位置信息(小端字节序)
        position = (recv_buffer[5] << 24) |
                   (recv_buffer[4] << 16) |
                   (recv_buffer[3] << 8) |
                   (recv_buffer[2]);
    }

    return position;
}

/**
 * @brief 计算校验和
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return 校验和
 */
uint16_t calculate_checksum(uint8_t *data, uint16_t len)
{
    uint16_t sum = 0;
    for (uint16_t i = 0; i < len; i++)
    {
        sum += data[i];
    }
    return sum;
}

/**
 * @brief 保存系统配置到Flash
 */
void save_config_to_flash(void)
{
    // 更新校验和
    sys_config.checksum = calculate_checksum((uint8_t *)&sys_config, sizeof(SystemConfig_t) - 2);

    // 解锁Flash
    HAL_FLASH_Unlock();

    // 擦除扇区
    FLASH_EraseInitTypeDef erase;
    erase.TypeErase = FLASH_TYPEERASE_SECTORS;
    erase.Sector = FLASH_SECTOR;
    erase.NbSectors = 1;
    erase.VoltageRange = FLASH_VOLTAGE_RANGE_3;

    uint32_t sector_error = 0;
    HAL_FLASHEx_Erase(&erase, &sector_error);

    // 写入配置数据
    uint32_t *data_ptr = (uint32_t *)&sys_config;
    uint32_t address = FLASH_ADDRESS;

    for (uint32_t i = 0; i < sizeof(SystemConfig_t) / 4; i++)
    {
        HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, address, data_ptr[i]);
        address += 4;
    }

    // 锁定Flash
    HAL_FLASH_Lock();
}

/**
 * @brief 从Flash加载系统配置
 * @return 1:成功加载 0:加载失败(数据无效)
 */
uint8_t load_config_from_flash(void)
{
    // 从Flash读取配置数据
    memcpy(&sys_config, (void *)FLASH_ADDRESS, sizeof(SystemConfig_t));

    // 验证魔术数字和校验和
    uint16_t calculated_checksum = calculate_checksum((uint8_t *)&sys_config, sizeof(SystemConfig_t) - 2);

    if (sys_config.magic_number == 0xA5A5A5A5 &&
        calculated_checksum == sys_config.checksum)
    {
        // 数据有效,应用配置
        motor_x = sys_config.saved_x;
        motor_y = sys_config.saved_y;
        return 1;
    }

    // 数据无效,使用默认配置
    return 0;
}

/**
 * @brief 保存初始位置
 */
void save_initial_position(void)
{
    // 获取并保存初始位置
    motor_x.initial_pos = read_motor_position(&huart2, X_MOTOR_ADDR);
    motor_y.initial_pos = read_motor_position(&huart5, Y_MOTOR_ADDR);

    // 更新配置
    sys_config.saved_x.initial_pos = motor_x.initial_pos;
    sys_config.saved_y.initial_pos = motor_y.initial_pos;

    // 存储到Flash
    save_config_to_flash();
}

/**
 * @brief 进行图像处理寻找目标
 * @param image 图像数据
 * @param width 图像宽度
 * @param height 图像高度
 */
void process_image(uint8_t *image, uint16_t width, uint16_t height)
{
    int32_t sum_x = 0, sum_y = 0;
    int32_t pixel_count = 0;

    // 计算目标质心
    for (uint16_t y = 0; y < height; y++)
    {
        for (uint16_t x = 0; x < width; x++)
        {
            uint16_t pos = y * width + x;
            uint8_t byte_pos = pos / 8;
            uint8_t bit_pos = pos % 8;

            // 检查像素是否为1(目标点)
            if (image[byte_pos] & (1 << bit_pos))
            {
                sum_x += x;
                sum_y += y;
                pixel_count++;
            }
        }
    }

    // 计算目标中心
    if (pixel_count > 10)
    { // 至少10个像素才认为找到目标
        camera_data.center_x = sum_x / pixel_count;
        camera_data.center_y = sum_y / pixel_count;
        camera_data.target_found = 1;
    }
    else
    {
        camera_data.target_found = 0;
    }
}

/**
 * @brief 计算校验和
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和
 */
uint8_t calculate_frame_checksum(uint8_t *data, uint16_t len)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < len; i++)
    {
        checksum ^= data[i];
    }
    return checksum;
}

/**
 * @brief 处理矩形检测数据
 * @param rect_data 矩形数据指针
 * @return 处理结果 0-成功 1-失败
 */
uint8_t process_rect_data(RectData_t *rect_data)
{
    // 校验数据完整性
    uint8_t calc_checksum = calculate_frame_checksum((uint8_t*)rect_data, sizeof(RectData_t) - 1);
    if (calc_checksum != rect_data->checksum)
    {
        return 1; // 校验失败
    }

    // 验证数据合理性
    if (validate_rect_data(rect_data) != 0)
    {
        camera_data.target_found = 0;
        return 1;
    }

    // 检查置信度阈值
    if (rect_data->confidence < gimbal_control.confidence_threshold)
    {
        camera_data.target_found = 0;
        return 1;
    }

    // 更新摄像头数据
    camera_data.center_x = rect_data->center_x;
    camera_data.center_y = rect_data->center_y;
    camera_data.rect_x = rect_data->rect_x;
    camera_data.rect_y = rect_data->rect_y;
    camera_data.rect_width = rect_data->width;
    camera_data.rect_height = rect_data->height;
    camera_data.confidence = rect_data->confidence;
    camera_data.frame_id = rect_data->frame_id;
    camera_data.target_found = 1;

    // 更新统计信息
    update_camera_stats(rect_data, 1);

    return 0;
}

/**
 * @brief 处理接收到的摄像头数据
 * @param data 接收到的数据
 * @param len 数据长度
 */
void process_camera_data(uint8_t *data, uint16_t len)
{
    static uint8_t frame_buffer[CAM_MAX_FRAME_SIZE];
    static uint16_t recv_count = 0;
    static uint8_t frame_state = 0; // 0-等待帧头 1-接收数据

    for (uint16_t i = 0; i < len; i++)
    {
        switch (frame_state)
        {
        case 0: // 等待帧头
            if (recv_count == 0 && data[i] == CAM_FRAME_HEADER1)
            {
                frame_buffer[recv_count++] = data[i];
            }
            else if (recv_count == 1 && data[i] == CAM_FRAME_HEADER2)
            {
                frame_buffer[recv_count++] = data[i];
                frame_state = 1;
            }
            else
            {
                recv_count = 0; // 重置
            }
            break;

        case 1: // 接收数据
            frame_buffer[recv_count++] = data[i];

            // 检查是否接收完整帧
            if (recv_count >= 4) // 至少有帧头+命令+长度
            {
                CameraFrame_t *frame = (CameraFrame_t*)frame_buffer;
                uint16_t expected_len = 4 + frame->length; // 帧头+命令+长度+数据

                if (recv_count >= expected_len)
                {
                    // 处理完整帧
                    switch (frame->cmd)
                    {
                    case CAM_CMD_RECT_DATA:
                        if (frame->length == sizeof(RectData_t))
                        {
                            process_rect_data((RectData_t*)frame->data);
                        }
                        break;

                    case CAM_CMD_IMAGE_DATA:
                        // 处理图像数据（如果需要）
                        if (frame->length <= sizeof(camera_data.frame_buffer))
                        {
                            memcpy(camera_data.frame_buffer, frame->data, frame->length);
                            process_image(camera_data.frame_buffer, CAM_WIDTH, CAM_HEIGHT);
                        }
                        break;

                    case CAM_CMD_STATUS:
                        // 处理状态信息
                        break;

                    default:
                        break;
                    }

                    // 重置状态
                    recv_count = 0;
                    frame_state = 0;
                }
                else if (recv_count >= CAM_MAX_FRAME_SIZE)
                {
                    // 缓冲区溢出，重置
                    recv_count = 0;
                    frame_state = 0;
                }
            }
            break;
        }
    }
}

/**
 * @brief 自动跟踪控制 - 改进版本，支持矩形中心精确瞄准
 */
void auto_track_control(void)
{
    if (!sys_config.auto_track || !camera_data.target_found)
    {
        return;
    }

    // 计算屏幕中心偏移（像素坐标）
    int16_t screen_center_x = CAM_WIDTH / 2;
    int16_t screen_center_y = CAM_HEIGHT / 2;
    int16_t error_x = camera_data.center_x - screen_center_x;
    int16_t error_y = camera_data.center_y - screen_center_y;

    // 根据置信度调整控制强度
    float confidence_factor = camera_data.confidence / 100.0f;
    if (confidence_factor < 0.3f) confidence_factor = 0.3f; // 最小控制强度
    if (confidence_factor > 1.0f) confidence_factor = 1.0f; // 最大控制强度

    // 死区处理 - 避免小幅抖动
    if (abs(error_x) < gimbal_control.dead_zone_x) error_x = 0;
    if (abs(error_y) < gimbal_control.dead_zone_y) error_y = 0;

    // 如果误差很小，不需要调整
    if (error_x == 0 && error_y == 0)
    {
        return;
    }

    // 像素误差转换为电机步数的比例因子
    // 这个值需要根据实际的摄像头视野角度和电机分辨率调整

    // 使用PID计算电机调整量
    float adjust_x = calculate_pid(&pid_x, error_x, 0) * confidence_factor;
    float adjust_y = calculate_pid(&pid_y, error_y, 0) * confidence_factor;

    // 转换为电机步数
    int32_t step_x = (int32_t)(adjust_x * gimbal_control.pixel_to_step_x);
    int32_t step_y = (int32_t)(adjust_y * gimbal_control.pixel_to_step_y);

    // 限制单次调整幅度，避免过大的跳跃
    if (step_x > gimbal_control.max_step_per_cycle) step_x = gimbal_control.max_step_per_cycle;
    if (step_x < -gimbal_control.max_step_per_cycle) step_x = -gimbal_control.max_step_per_cycle;
    if (step_y > gimbal_control.max_step_per_cycle) step_y = gimbal_control.max_step_per_cycle;
    if (step_y < -gimbal_control.max_step_per_cycle) step_y = -gimbal_control.max_step_per_cycle;

    // 更新电机目标位置
    motor_x.target_pos = motor_x.current_pos - step_x; // 注意方向
    motor_y.target_pos = motor_y.current_pos - step_y; // 注意方向

    // 限制位置范围
    if (motor_x.target_pos > MAX_POSITION_X)
        motor_x.target_pos = MAX_POSITION_X;
    else if (motor_x.target_pos < -MAX_POSITION_X)
        motor_x.target_pos = -MAX_POSITION_X;

    if (motor_y.target_pos > MAX_POSITION_Y)
        motor_y.target_pos = MAX_POSITION_Y;
    else if (motor_y.target_pos < -MAX_POSITION_Y)
        motor_y.target_pos = -MAX_POSITION_Y;

    // 根据误差大小调整电机速度
    uint16_t speed_x = motor_x.speed;
    uint16_t speed_y = motor_y.speed;

    // 误差越大，速度越快（在合理范围内）
    if (abs(error_x) > 50)
    {
        speed_x = motor_x.speed * 1.5f;
        if (speed_x > 2000) speed_x = 2000; // 限制最大速度
    }
    else if (abs(error_x) < 20)
    {
        speed_x = motor_x.speed * 0.5f;
        if (speed_x < 200) speed_x = 200; // 限制最小速度
    }

    if (abs(error_y) > 50)
    {
        speed_y = motor_y.speed * 1.5f;
        if (speed_y > 2000) speed_y = 2000;
    }
    else if (abs(error_y) < 20)
    {
        speed_y = motor_y.speed * 0.5f;
        if (speed_y < 200) speed_y = 200;
    }

    // 发送电机控制命令
    motor_move_to_position(&huart2, X_MOTOR_ADDR, motor_x.target_pos, speed_x, motor_x.acc);
    motor_move_to_position(&huart5, Y_MOTOR_ADDR, motor_y.target_pos, speed_y, motor_y.acc);
}

/**
 * @brief 处理串口接收数据
 * @param data 接收到的数据
 * @param len 数据长度
 */
void process_uart_data(uint8_t *data, uint16_t len)
{
    if (len > 0 && data[0] == '$')
    {
        switch (data[1])
        {
        case 'M': // 手动控制命令
            if (len >= 6)
            {
                int16_t x_offset = (data[2] << 8) | data[3];
                int16_t y_offset = (data[4] << 8) | data[5];

                // 更新目标位置
                motor_x.target_pos = motor_x.current_pos + x_offset;
                motor_y.target_pos = motor_y.current_pos + y_offset;

                // 发送控制命令
                motor_move_to_position(&huart2, X_MOTOR_ADDR, motor_x.target_pos, motor_x.speed, motor_x.acc);
                motor_move_to_position(&huart5, Y_MOTOR_ADDR, motor_y.target_pos, motor_y.speed, motor_y.acc);
            }
            break;

        case 'H': // 回原点命令
            motor_x.target_pos = motor_x.initial_pos;
            motor_y.target_pos = motor_y.initial_pos;
            motor_move_to_position(&huart2, X_MOTOR_ADDR, motor_x.target_pos, motor_x.speed, motor_x.acc);
            motor_move_to_position(&huart5, Y_MOTOR_ADDR, motor_y.target_pos, motor_y.speed, motor_y.acc);
            break;

        case 'A': // 自动跟踪开关
            if (len >= 3)
            {
                sys_config.auto_track = data[2];
                save_config_to_flash();
            }
            break;

        case 'S': // 保存当前位置为初始位置
            save_initial_position();
            break;

        case 'D': // 调试信息输出
            if (len >= 3)
            {
                if (data[2] == 1) // 输出摄像头状态
                {
                    uart_print("CAM: Found=%d, X=%d, Y=%d, W=%d, H=%d, Conf=%d, ID=%lu\r\n",
                              camera_data.target_found,
                              camera_data.center_x,
                              camera_data.center_y,
                              camera_data.rect_width,
                              camera_data.rect_height,
                              camera_data.confidence,
                              camera_data.frame_id);
                }
                else if (data[2] == 2) // 输出电机状态
                {
                    uart_print("MOTOR: X_cur=%ld, X_tar=%ld, Y_cur=%ld, Y_tar=%ld\r\n",
                              motor_x.current_pos,
                              motor_x.target_pos,
                              motor_y.current_pos,
                              motor_y.target_pos);
                }
            }
            break;

        case 'C': // 摄像头控制命令
            if (len >= 4)
            {
                // 发送控制命令到摄像头
                uint8_t cam_cmd[8];
                cam_cmd[0] = CAM_FRAME_HEADER1;
                cam_cmd[1] = CAM_FRAME_HEADER2;
                cam_cmd[2] = data[2]; // 命令类型
                cam_cmd[3] = data[3]; // 参数

                // 通过USART3发送到摄像头
                HAL_UART_Transmit(&huart3, cam_cmd, 4, UART_TIMEOUT);
            }
            break;

        case 'L': // 循迹控制命令
            if (len >= 3)
            {
                switch(data[2])
                {
                    case '1': // 启动循迹
                        line_config.enabled = 1;
                        uart_print("Line following started\r\n");
                        break;

                    case '0': // 停止循迹
                        line_config.enabled = 0;
                        stop_motors();
                        uart_print("Line following stopped\r\n");
                        break;

                    case 'S': // 显示传感器状态
                    case 's':
                        {
                            char status_str[512];
                            get_line_sensor_status(status_str, sizeof(status_str));
                            uart_print("%s", status_str);
                        }
                        break;

                    case 'T': // 显示统计信息
                    case 't':
                        {
                            char stats_str[512];
                            get_line_follow_stats(stats_str, sizeof(stats_str));
                            uart_print("%s", stats_str);
                        }
                        break;

                    case 'C': // 重新校准传感器
                    case 'c':
                        line_config.enabled = 0; // 停止循迹
                        stop_motors();
                        uart_print("Starting sensor calibration...\r\n");
                        calibrate_line_sensors(5000);
                        uart_print("Calibration complete\r\n");
                        break;

                    default:
                        uart_print("Unknown line following command: %c\r\n", data[2]);
                        break;
                }
            }
            break;

        default:
            break;
        }
    }
}

/**
 * @brief OLED显示任务,定时刷新显示内容
 */
void oled_task(MultiTimer *timer, void *userData)
{
    // 清屏
    ssd1306_basic_clear();

    // 显示计数和位置信息
    sprintf(buffer, "Count: %d", count++);
    ssd1306_basic_string(0, 0, buffer, (uint16_t)strlen(buffer), 1, SSD1306_FONT_12);

    sprintf(buffer, "X: %ld", motor_x.current_pos);
    ssd1306_basic_string(0, 15, buffer, (uint16_t)strlen(buffer), 1, SSD1306_FONT_12);

    sprintf(buffer, "Y: %ld", motor_y.current_pos);
    ssd1306_basic_string(0, 30, buffer, (uint16_t)strlen(buffer), 1, SSD1306_FONT_12);

    // 显示跟踪状态
    sprintf(buffer, "Track: %s", sys_config.auto_track ? "ON" : "OFF");
    ssd1306_basic_string(0, 45, buffer, (uint16_t)strlen(buffer), 1, SSD1306_FONT_12);

    // 重启定时器
    multiTimerStart(timer, OLED_TASK_TIME, oled_task, NULL);
}

/**
 * @brief 电机控制任务,处理伺服电机定位与驱动
 */
void motor_task(MultiTimer *timer, void *userData)
{
    // 读取电机位置
    motor_x.current_pos = read_motor_position(&huart2, X_MOTOR_ADDR);
    motor_y.current_pos = read_motor_position(&huart5, Y_MOTOR_ADDR);

    // 自动跟踪控制
    if (sys_config.auto_track)
    {
        auto_track_control();
    }

    // 重启定时器
    multiTimerStart(timer, MOTOR_TASK_TIME, motor_task, NULL);
}

/**
 * @brief PID控制任务,用于电机精确定位
 */
void pid_task(MultiTimer *timer, void *userData)
{
    uint8_t data_x[8], data_y[8];

    // 从环形缓冲区读取数据
    if (rt_ringbuffer_get(&ringbuffer_x, data_x, sizeof(data_x)) > 0)
    {
        // 处理X轴PID控制
        int16_t target_x = (data_x[0] << 8) | data_x[1];
        float output_x = calculate_pid(&pid_x, motor_x.current_pos, target_x);

        // 应用控制输出
        if (fabs(output_x) > 5.0f)
        { // 避免小幅抖动
            motor_x.target_pos = motor_x.current_pos + (int32_t)output_x;
            motor_move_to_position(&huart2, X_MOTOR_ADDR, motor_x.target_pos, motor_x.speed, motor_x.acc);
        }
    }

    if (rt_ringbuffer_get(&ringbuffer_y, data_y, sizeof(data_y)) > 0)
    {
        // 处理Y轴PID控制
        int16_t target_y = (data_y[0] << 8) | data_y[1];
        float output_y = calculate_pid(&pid_y, motor_y.current_pos, target_y);

        // 应用控制输出
        if (fabs(output_y) > 5.0f)
        { // 避免小幅抖动
            motor_y.target_pos = motor_y.current_pos + (int32_t)output_y;
            motor_move_to_position(&huart5, Y_MOTOR_ADDR, motor_y.target_pos, motor_y.speed, motor_y.acc);
        }
    }

    // 重启定时器
    multiTimerStart(timer, PID_TASK_TIME, pid_task, NULL);
}

/**
 * @brief 摄像头数据处理任务
 */
void camera_task(MultiTimer *timer, void *userData)
{
    uint8_t cam_data[64]; // 增加缓冲区大小以处理更大的数据包
    uint16_t data_len;

    // 从摄像头缓冲区读取数据
    data_len = rt_ringbuffer_get(&ringbuffer_cam, cam_data, sizeof(cam_data));
    if (data_len > 0)
    {
        process_camera_data(cam_data, data_len);
    }

    // 重启定时器
    multiTimerStart(timer, CAMERA_TASK_TIME, camera_task, NULL);
}

/**
 * @brief 用户交互任务
 */
void user_task(MultiTimer *timer, void *userData)
{
    uint8_t user_data[16];

    // 从用户命令缓冲区读取数据
    if (rt_ringbuffer_get(&ringbuffer_user, user_data, sizeof(user_data)) > 0)
    {
        process_uart_data(user_data, sizeof(user_data));
    }

    // 重启定时器
    multiTimerStart(timer, USER_TASK_TIME, user_task, NULL);
}

/**
 * @brief 循迹控制任务
 */
void line_follow_task(MultiTimer *timer, void *userData)
{
    // 执行循迹控制
    line_following_task();

    // 重启定时器
    multiTimerStart(timer, LINE_FOLLOW_TASK_TIME, line_follow_task, NULL);
}

/**
 * @brief 系统初始化
 */
void system_init(void)
{
    // 延时初始化
    delay_init();

    // 环形缓冲区初始化
    rt_ringbuffer_init(&ringbuffer_y, ringbuffer_pool_y, sizeof(ringbuffer_pool_y));
    rt_ringbuffer_init(&ringbuffer_x, ringbuffer_pool_x, sizeof(ringbuffer_pool_x));
    rt_ringbuffer_init(&ringbuffer_cam, ringbuffer_pool_cam, sizeof(ringbuffer_pool_cam));
    rt_ringbuffer_init(&ringbuffer_user, ringbuffer_pool_user, sizeof(ringbuffer_pool_user));

    // OLED显示初始化
    ssd1306_basic_init(SSD1306_INTERFACE_SPI, SSD1306_ADDR_SA0_0);
    ssd1306_basic_clear();

    // 电机初始化
    Motor_Init();
    Emm_V5_Reset_CurPos_To_Zero(&huart5, Y_MOTOR_ADDR);
    Emm_V5_Reset_CurPos_To_Zero(&huart2, X_MOTOR_ADDR);

    // 初始化电机参数
    motor_x.speed = MOTOR_SPEED_DEFAULT;
    motor_x.acc = MOTOR_ACC_DEFAULT;
    motor_x.is_running = 0;

    motor_y.speed = MOTOR_SPEED_DEFAULT;
    motor_y.acc = MOTOR_ACC_DEFAULT;
    motor_y.is_running = 0;

    // 初始化PID控制器
    init_pid(&pid_x);
    init_pid(&pid_y);

    // 初始化云台控制参数
    init_gimbal_control();

    // 初始化摄像头数据
    memset(&camera_data, 0, sizeof(Camera_t));

    // 初始化ADC (用于灰度传感器)
    MX_ADC1_Init();

    // 初始化循迹系统
    line_following_init();

    // 启动ADC连续转换
    start_adc_conversion();

    // 加载配置
    if (!load_config_from_flash())
    {
        // 配置无效,保存初始位置
        save_initial_position();
    }
}

/**
 * @brief 启动所有任务
 */
void start_all_tasks(void)
{
    // 多任务定时器初始化
    multiTimerInstall(bsp_get_systick);

    // 启动各个任务
    multiTimerStart(&mt_oled, OLED_TASK_TIME, oled_task, NULL);
    multiTimerStart(&mt_pid, PID_TASK_TIME, pid_task, NULL);
    multiTimerStart(&mt_cam, CAMERA_TASK_TIME, camera_task, NULL);
    multiTimerStart(&mt_user, USER_TASK_TIME, user_task, NULL);
    multiTimerStart(&mt_usart, MOTOR_TASK_TIME, motor_task, NULL);
    multiTimerStart(&mt_line_follow, LINE_FOLLOW_TASK_TIME, line_follow_task, NULL);
}

/**
 * @brief UART接收回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    static uint8_t rx_buffer[1];

    if (huart->Instance == USART1)
    {
        // 用户命令接口
        rt_ringbuffer_put(&ringbuffer_user, rx_buffer, 1);
        HAL_UART_Receive_IT(huart, rx_buffer, 1);
    }
    else if (huart->Instance == USART3)
    {
        // 摄像头数据接口
        rt_ringbuffer_put(&ringbuffer_cam, rx_buffer, 1);
        HAL_UART_Receive_IT(huart, rx_buffer, 1);
    }
}

/**
 * @brief 主程序入口
 */
int main(void)
{
    // 外设初始化 (由CubeMX生成)
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_DMA_Init();
    MX_UART5_Init();
    MX_USART2_UART_Init();
    MX_SPI1_Init();
    MX_USART1_UART_Init();
    MX_USART3_UART_Init();
    MX_ADC1_Init();

    // 系统初始化
    system_init();

    // 启动所有任务
    start_all_tasks();

    // 启动UART中断接收
    HAL_UART_Receive_IT(&huart1, rx_buffer_user, 1);
    HAL_UART_Receive_IT(&huart3, rx_buffer_cam, 1);

    // 主循环
    while (1)
    {
        // 多任务调度
        multiTimerYield();
    }
}